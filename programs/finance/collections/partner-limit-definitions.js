export default {
    name: 'partner-limit-definitions',
    title: 'Partner Limit Definitions',
    order: true,
    cache: true,
    localCache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code'
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        partnerType: {
            type: 'string',
            label: 'Partner type'
        },
        partnerGroupId: {
            type: 'string',
            label: 'Partner group',
            required: false
        },
        enableLimitChecks: {
            type: 'boolean',
            label: 'Enable limit checks',
            default: true
        },
        limitControlDocument: {
            type: 'string',
            label: 'Limit control document',
            default: 'order'
        },
        limitOrderControl: {
            type: 'string',
            label: 'Limit order control',
            default: 'warn'
        },
        limitQuotationControl: {
            type: 'string',
            label: 'Limit quotation control',
            default: 'warn'
        },
        limitInvoiceControl: {
            type: 'string',
            label: 'Limit invoice control',
            default: 'block'
        },
        openAccountLimit: {
            type: 'decimal',
            label: 'Open account limit',
            default: 0
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    },
    attributes: {
        partnerGroup: {
            collection: 'kernel.partner-groups',
            parentField: 'partnerGroupId',
            childField: '_id'
        }
    }
};
