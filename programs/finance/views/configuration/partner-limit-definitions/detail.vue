<template>
    <ui-view type="form"
             collection="finance.partner-limit-definitions"
             :model="model"
             :title="title"
             :dialog-width="920"
             :dialog-height="dialogHeight">
        <div ref="container" v-resize="handleResize">
            <div class="columns">
                <div class="column is-half">
                    <ui-field name="code"/>
                    <ui-field name="name"/>
                    <ui-field name="partnerType"
                              :options="partnerTypeOptions"
                              translate-labels/>
                    <ui-field name="partnerGroupId"
                              collection="kernel.partner-groups"
                              view="system.management.configuration.partner-groups"
                              :label="partnerGroupIdLabel"
                              :update-params="updatePartnerGroupIdParams"
                              :filters="partnerGroupIdFilters"/>
                    <ui-field name="enableLimitChecks"/>
                </div>

                <div class="column is-half">
                    <ui-field name="limitControlDocument"
                              :options="limitControlDocumentOptions"
                              translate-labels/>
                    <ui-field name="limitOrderControl"
                              :options="limitControlOptions"
                              translate-labels
                              v-show="model.limitControlDocument === 'order'"/>
                    <ui-field name="limitQuotationControl"
                              :options="limitControlOptions"
                              translate-labels
                              v-show="model.limitControlDocument === 'quotation'"/>
                    <ui-field name="limitInvoiceControl"
                              :options="limitControlOptions"
                              translate-labels/>
                    <ui-field name="openAccountLimit" :precision="$setting('system.currencyPrecision')"/>
                    <ui-field name="isActive"/>
                </div>
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';

export default {
    data: () => ({
        model: {},
        partnerTypeOptions: [
            {value: 'customer', label: 'Customer'},
            {value: 'vendor', label: 'Vendor'}
        ],
        limitControlDocumentOptions: [
            {value: 'order', label: 'Order'},
            {value: 'quotation', label: 'Quotation'},
            {value: 'invoice', label: 'Invoice'}
        ],
        limitControlOptions: [
            {value: 'none', label: 'None'},
            {value: 'warn', label: 'Warn'},
            {value: 'block', label: 'Block'}
        ],
        dialogHeight: 260
    }),

    computed: {
        title() {
            const model = this.model;

            if (this.$params('id') && !model.name) return '';

            return model.name ? model.name : this.$t('New Partner Limit Definition');
        },
        partnerGroupIdLabel() {
            let label = this.$t('Partner group');

            if (this.model.partnerType === 'customer') {
                label = this.$t('Customer group');
            } else if (this.model.partnerType === 'vendor') {
                label = this.$t('Vendor group');
            }

            return label;
        },
        partnerGroupIdFilters() {
            return {type: this.model.partnerType};
        }
    },

    methods: {
        handleResize() {
            this.dialogHeight = this.$refs.container.clientHeight + 105;
        },

        updatePartnerGroupIdParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: this.model.partnerType};
            } else if (type === 'list') {
                params.filters = {type: this.model.partnerType};
            }

            return params;
        }
    }
};
</script>
