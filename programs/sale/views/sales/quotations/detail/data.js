import _ from 'lodash';

export default {
    data: () => ({
        model: {},
        activeTab: 'items',
        extraFields: [
            'status',
            'currencyRate',
            'isExpired',
            'subTotal',
            'taxTotal',
            'rounding',
            'grandTotal',
            'appliedTaxes',
            'discount',
            'discountAmount',
            'subTotalAfterDiscount',
            'paymentTerm',
            'paymentPlan',
            'paymentPlanBackup',
            'probability',
            'competitors',
            'stageHistory',
            'relatedDocuments',
            'stages',
            'createdAt',
            'convertedAt',
            'partialDeliveries',
            'contractParams',
            'workflowApprovalStatus',
            'campaigns',
            'freight',
            'additionalInformation',
            'additionalInformationId',
            'limitParams'
        ],
        generatedCode: '',
        currencyFormat: {},
        organizations: [],
        documentType: null,
        itemsKey: _.uniqueId('quotationItems_'),
        isItemsShown: true,
        organizationTeam: [],
        organizationSettings: {},
        deliveryPolicyOptions: [
            {value: 'when-one-ready', label: 'When a product is ready'},
            {value: 'when-all-ready', label: 'When all products are ready'}
        ],
        deliveryPriorityOptions: [
            {value: 'not-urgent', label: 'Not urgent'},
            {value: 'normal', label: 'Normal'},
            {value: 'urgent', label: 'Urgent'},
            {value: 'very-urgent', label: 'Very urgent'}
        ],
        shippingPaymentTypeOptions: [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ],
        productFields: [
            'code',
            'name',
            'displayName',
            'definition',
            'type',
            'baseUnitId',
            'categoryPath',
            'groupIds',
            'salesUnitId',
            'barcode',
            'unitRatios',
            'unitConversions',
            'salesPrice',
            'salesTaxId',
            'salesNote',
            'isSimple',
            'isKit',
            'brandId',
            'shippingUnitId',
            'typeOfGoodsId',
            'manufacturerId',
            'manufacturerProductCode',
            'countryOfManufactureId',
            'containerTypeId',
            'containerNo',
            'containerBrand',
            'hsCode',
            'classificationCode',
            'classificationVersion',
            'classificationValue',
            'countryOfOriginId',
            'unitMeasurements',
            'tracking'
        ],
        stages: [],
        activeStageIndex: null,
        selectedCompetitorId: null,
        nearbyDocumentsCount: 0,
        currentCurrencyRate: 1,
        isModelLoaded: false,
        deliveryMethodType: null,
        systemCurrencyId: null,
        additionalItemFields: [],
        currencies: [],
        showAdditionalInformationTab: false,
        initialized: false
    })
};
