<template>
    <ui-view
        ref="view"
        type="form"
        :class="{
            'sale-quotations-detail-form': true,
            'no-stages': stages.length < 1
        }"
        collection="sale.quotations"
        method="sale.save-quotation"
        :model="model"
        :schema="schema"
        :title="title"
        :extra-fields="extraFields"
        :activity-payload="activityPayload"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        :after-submit="afterSubmit"
        :on-error="onError"
        :actions="actions"
        :extra-actions="extraActions"
        :print-payload="printPayload"
        :mail="getMailPayload"
        :assignation="assignation"
        :revisions="revisions"
        @changed="handleChange"
        v-if="initialized"
    >
        <template slot="form-top">
            <ui-status :statuses="statuses" :value="status">
                <div
                    class="quotation-validity-status-container"
                    v-if="
                        !!$setting('sale.forceQuotationValidity') &&
                        !!$params('id') &&
                        !!isModelLoaded &&
                        !(
                            status === 'won' ||
                            status === 'lost' ||
                            status === 'lost-to-competitor' ||
                            status === 'canceled'
                        )
                    "
                >
                    <div class="quotation-validity-status is-valid" v-if="!isExpired">
                        <i class="fal fa-hourglass-start"></i><span>{{ 'Is Valid' | t }}</span>
                    </div>
                    <div class="quotation-validity-status is-expired" v-else-if="isExpired">
                        <i class="fal fa-hourglass-end"></i><span>{{ 'Is Expired' | t }}</span>
                    </div>
                </div>
            </ui-status>

            <limit-progress-indicator :params="model.limitParams" />

            <ui-related-documents :documents="model.relatedDocuments" />

            <crm-components-stopwatch :model="model" document="quotation" />

            <el-badge :value="nearbyDocumentsCount" v-if="nearbyDocumentsCount > 0">
                <el-button plain icon="fal fa-radar" @click="handleOpenNearbyDocuments">
                    {{ 'Nearby Documents' | t }}
                </el-button>
            </el-badge>

            <template
                v-if="
                    !!$setting('sale.forceQuotationValidity') &&
                    isExpired &&
                    !!$params('id') &&
                    !(status === 'won' || status === 'lost' || status === 'lost-to-competitor' || status === 'canceled')
                "
            >
                <el-button
                    :loading="$params('loading')"
                    type="success"
                    icon="far fa-hourglass-half"
                    :disabled="
                        !$params('isPreview') ||
                        !$params('id') ||
                        $params('loading') ||
                        (!!model.workflowApprovalStatus && model.workflowApprovalStatus === 'waiting-for-approval')
                    "
                    @click="handleRenewValidity"
                >
                    {{ 'Renew Validity' | t }}
                </el-button>

                <el-button
                    :loading="$params('loading')"
                    plain
                    icon="far fa-thumbs-down"
                    :disabled="
                        !$params('isPreview') ||
                        (!!model.workflowApprovalStatus && model.workflowApprovalStatus === 'waiting-for-approval')
                    "
                    @click="handleLost"
                >
                    {{ 'Lost' | t }}
                </el-button>
            </template>
            <template v-else>
                <el-button
                    :loading="$params('loading')"
                    type="primary"
                    icon="far fa-thumbs-up"
                    :disabled="
                        !$params('isPreview') ||
                        !$params('id') ||
                        status === 'won' ||
                        status === 'lost' ||
                        status === 'lost-to-competitor' ||
                        status === 'canceled' ||
                        $params('loading') ||
                        (!!model.workflowApprovalStatus && model.workflowApprovalStatus === 'waiting-for-approval')
                    "
                    @click="handleWon"
                >
                    {{ 'Won' | t }}
                </el-button>
                <el-button
                    :loading="$params('loading')"
                    plain
                    icon="far fa-thumbs-down"
                    :disabled="
                        !$params('isPreview') ||
                        !$params('id') ||
                        status === 'won' ||
                        status === 'lost' ||
                        status === 'lost-to-competitor' ||
                        status === 'canceled' ||
                        $params('loading') ||
                        (!!model.workflowApprovalStatus && model.workflowApprovalStatus === 'waiting-for-approval')
                    "
                    @click="handleLost"
                >
                    {{ 'Lost' | t }}
                </el-button>
            </template>
        </template>

        <div class="quotation-header" v-show="stages.length > 0">
            <div class="quotation-probability">
                <el-progress type="circle" :percentage="model.probability || 0" :stroke-width="4" :width="40" />
            </div>

            <el-steps
                class="quotation-steps"
                :class="{'is-disabled': $params('isPreview')}"
                :active="activeStageIndex"
                align-center
                finish-status="success"
                v-show="stepStages && stepStages.length > 0"
            >
                <el-step v-for="(stage, index) in stepStages" :key="index" :title="stage.name" :data-index="index" />
            </el-steps>

            <div class="quotation-amount">
                <div class="top-report">
                    <div class="report-label">{{ 'Grand Total' | t }}</div>
                    <div class="report-value">{{ $format(model.grandTotal, 'currency', currencyFormat) }}</div>
                </div>
            </div>
        </div>

        <div class="columns">
            <div class="column is-half">
                <ui-field name="module" v-show="false" />
                <ui-field name="documentTypeId" collection="sale.document-types" :filters="documentTypeIdFilters" />
                <ui-field name="code" disabled />
                <ui-field
                    name="partnerType"
                    :options="partnerTypeOptions"
                    :disabled="status === 'payment-planned'"
                    translate-labels
                />
                <ui-field
                    name="partnerGroupId"
                    collection="kernel.partner-groups"
                    view="system.management.configuration.partner-groups"
                    disable-create
                    disable-detail
                    :filters="partnerGroupIdFilters"
                    :disabled="status === 'payment-planned'"
                    v-show="model.partnerType === 'customer'"
                />
                <kernel-common-partner-select
                    name="partnerId"
                    :filters="partnerIdFilters"
                    :update-params="updatePartnerIdParams"
                    :disabled="isPartnerIdDisabled"
                    v-show="model.partnerType === 'customer'"
                />
                <ui-field
                    name="contactPersonId"
                    collection="kernel.contacts"
                    view="partners.contacts"
                    :filters="{partnerId: model.partnerId, type: 'contact'}"
                    :update-params="updateContactIdParams"
                    :disabled="!model.partnerId"
                    v-show="model.partnerType === 'customer'"
                />
                <ui-field
                    name="leadId"
                    collection="crm.leads"
                    view="crm.customer-relations.leads"
                    :filters="{status: {$ne: 'canceled'}}"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    :disabled="status === 'payment-planned'"
                    v-show="model.partnerType === 'lead'"
                />
                <ui-field name="reference" />
                <ui-field
                    name="stageId"
                    :options="stageIdOptions"
                    :html-template="stageIdOptionsTemplate"
                    v-show="stages.length > 0"
                />
            </div>
            <div class="column is-half">
                <div style="display: flex; max-width: 450px" v-show="$setting('system.multiBranch')">
                    <kernel-common-branch-select
                        style="flex: 1 1 0"
                        :disabled="status === 'payment-planned' || !(!!model.partnerId || !!model.leadId)"
                    />
                    <el-button
                        class="mb5 ml5"
                        style="flex: 0 0 25px"
                        icon="far fa-check"
                        :title="'Apply To All' | t"
                        :disabled="
                            status === 'payment-planned' ||
                            $params('isPreview') ||
                            !(!!model.partnerId || !!model.leadId)
                        "
                        @click="handleApplyBranch"
                    />
                </div>
                <div class="ui-inline-fields" v-show="$setting('system.multiCurrency')">
                    <div class="field-label">{{ 'Currency' | t }}</div>
                    <div class="field-content">
                        <ui-field
                            name="currencyId"
                            collection="kernel.currencies"
                            label="hide"
                            :style="{flex: '1 1 0'}"
                            :disabled="status === 'payment-planned' || !(!!model.partnerId || !!model.leadId)"
                        />
                        <ui-field
                            name="currencyRate"
                            label="hide"
                            :style="{flex: '1 1 0'}"
                            :precision="$setting('system.exchangeRatePrecision')"
                            :disabled="
                                status === 'payment-planned' ||
                                model.currencyId === systemCurrencyId ||
                                !(!!model.partnerId || !!model.leadId)
                            "
                        />
                    </div>
                </div>
                <ui-field
                    name="recordDate"
                    :disabled="status === 'payment-planned' || !(!!model.partnerId || !!model.leadId)"
                />
                <ui-field
                    name="quotationDate"
                    :disabled="status === 'payment-planned' || !(!!model.partnerId || !!model.leadId)"
                />
                <ui-field
                    name="expiryDate"
                    :disabled="status === 'payment-planned' || !(!!model.partnerId || !!model.leadId)"
                />
                <ui-field
                    name="lostReasonId"
                    collection="crm.lost-reasons"
                    v-show="status === 'lost' || status === 'lost-to-competitor'"
                />
            </div>
        </div>

        <el-tabs v-model="activeTab">
            <el-tab-pane name="items" :label="'Items' | t">
                <ui-field
                    name="items"
                    class="mb0"
                    :key="itemsKey"
                    :min-empty-rows="3"
                    :before-init="beforeItemsInit"
                    :before-create="beforeSaveItem"
                    :before-update="beforeSaveItem"
                    :after-create="afterSaveItem"
                    :after-update="afterSaveItem"
                    :after-remove="afterRemoveItem"
                    :context-menu-actions="itemsContextMenuActions"
                    :enable-enlarge="true"
                    resizable
                    :enable-popup-edit="true"
                    detail-view="system.components.order-item-detail"
                    :update-params="updateItemsParams"
                    :disabled="!(!!model.partnerId || !!model.leadId) || status === 'payment-planned'"
                    v-if="isItemsShown"
                >
                    <template slot="actions">
                        <el-dropdown @command="handleCheckItems" trigger="click" placement="bottom-start">
                            <el-button :loading="$params('loading')" icon="far fa-check-circle" />
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                    command="check-availability"
                                    :disabled="
                                        $params('isPreview') ||
                                        (!model.partnerId && !model.leadId) ||
                                        (model.items || []).length < 1 ||
                                        !(status === 'draft' || status === 'payment-planned')
                                    "
                                >
                                    {{ 'Check Availability' | t }}
                                </el-dropdown-item>
                                <el-dropdown-item
                                    command="check-prices"
                                    :disabled="
                                        $params('isPreview') ||
                                        (!model.partnerId && !model.leadId) ||
                                        (model.items || []).length < 1 ||
                                        !(status === 'draft' || status === 'payment-planned')
                                    "
                                >
                                    {{ 'Check Prices' | t }}
                                </el-dropdown-item>
                                <el-dropdown-item
                                    command="update-exchange-rates"
                                    :disabled="
                                        $params('isPreview') ||
                                        (!model.partnerId && !model.leadId) ||
                                        (model.items || []).length < 1 ||
                                        !(status === 'draft' || status === 'payment-planned')
                                    "
                                >
                                    {{ 'Update Exchange Rates' | t }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>

                        <el-dropdown @command="handleUpdateItems" trigger="click" placement="bottom-start">
                            <el-button :loading="$params('loading')" icon="far fa-percent" />
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                    command="update-item-prices"
                                    :disabled="
                                        $params('isPreview') ||
                                        !model.partnerId ||
                                        (model.items || []).length < 1 ||
                                        !(status === 'draft' || status === 'payment-planned')
                                    "
                                >
                                    {{ 'Update Prices' | t }}
                                </el-dropdown-item>

                                <el-dropdown-item
                                    command="update-item-discounts"
                                    :disabled="
                                        $params('isPreview') ||
                                        !model.partnerId ||
                                        (model.items || []).length < 1 ||
                                        !(status === 'draft' || status === 'payment-planned')
                                    "
                                >
                                    {{ 'Update Discounts' | t }}
                                </el-dropdown-item>

                                <el-dropdown-item
                                    command="update-item-taxes"
                                    :disabled="
                                        $params('isPreview') ||
                                        !model.partnerId ||
                                        (model.items || []).length < 1 ||
                                        !(status === 'draft' || status === 'payment-planned')
                                    "
                                >
                                    {{ 'Update Taxes' | t }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>

                        <el-tooltip effect="dark" :content="'Add Multiple Products' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-layer-plus"
                                :disabled="
                                    $params('isPreview') ||
                                    !(!!model.partnerId || !!model.leadId) ||
                                    status === 'payment-planned'
                                "
                                @click="handleAddMultipleProducts"
                            />
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="'Add Kit Products' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-layer-plus"
                                :disabled="
                                    $params('isPreview') ||
                                    !(!!model.partnerId || !!model.leadId) ||
                                    status === 'payment-planned'
                                "
                                @click="handleAddKitProducts"
                            />
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="'Add Cluster Products' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-layer-group"
                                :disabled="
                                    $params('isPreview') ||
                                    !(!!model.partnerId || !!model.leadId) ||
                                    status === 'payment-planned'
                                "
                                @click="handleClusterProducts"
                            />
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="'Add Product With Barcode' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-barcode"
                                :disabled="
                                    $params('isPreview') ||
                                    !(!!model.partnerId || !!model.leadId) ||
                                    status === 'payment-planned'
                                "
                                @click="handleAddProductWithBarcode"
                            />
                        </el-tooltip>

                        <el-tooltip
                            effect="dark"
                            :content="'Import Items' | t"
                            placement="bottom"
                            v-show="!$params('isPreview')"
                        >
                            <el-uploader
                                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                @started="$params('loading', true)"
                                @completed="handleImportItems"
                                :disabled="!(!!model.partnerId || !!model.leadId) || $params('isPreview')"
                            >
                                <el-button
                                    :loading="$params('loading')"
                                    icon="far fa-cloud-upload"
                                    :disabled="!(!!model.partnerId || !!model.leadId) || $params('isPreview')"
                                />
                            </el-uploader>
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="'Remove All Items' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-broom"
                                :disabled="
                                    $params('isPreview') ||
                                    !(!!model.partnerId || !!model.leadId) ||
                                    status === 'payment-planned'
                                "
                                @click="handleRemoveAllItems"
                            />
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="$t('Unit Price Analysis')" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-money-check-edit"
                                @click="handleUnitPriceAnalysis"
                                v-show="!isUnitPriceAnalysisDisabled"
                            />
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="'Calculate Gross Profit' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-calculator"
                                @click="handleCalculateGrossProfit"
                                v-show="!isGrossProfitCalculatorDisabled"
                            />
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="'Partial Delivery' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-map-marked-alt"
                                @click="handlePartialDelivery"
                            />
                        </el-tooltip>

                        <el-button
                            :loading="$params('loading')"
                            plain
                            icon="far fa-coins"
                            style="float: right"
                            class="ml5"
                            @click="handlePaymentPlanned"
                            v-show="model.subTotal !== 0 && !!model.paymentTermId"
                        >
                            {{ 'Payment Plan' | t }}
                        </el-button>

                        <el-button
                            :loading="$params('loading')"
                            plain
                            icon="far fa-receipt"
                            style="float: right"
                            @click="handleOpenAutomaticPaymentPlan"
                            v-show="
                                (model.items || []).length > 0 &&
                                status !== 'lost-to-competitor' &&
                                status !== 'lost' &&
                                status !== 'won' &&
                                status !== 'canceled'
                            "
                        >
                            {{ 'Automatic Payment Plan' | t }}
                        </el-button>
                    </template>
                </ui-field>

                <ui-totals :totals="totalItems">
                    <el-tabs :value="'note'" style="max-width: 450px">
                        <el-tab-pane name="note" :label="'Quotation Note' | t">
                            <ui-field class="mt10" name="note" label="hide" :rows="2" />
                        </el-tab-pane>

                        <el-tab-pane
                            name="organization"
                            :label="'Organization Information' | t"
                            :disabled="!$setting('sale.salesOrganizations')"
                        >
                            <ui-field
                                class="mt10"
                                name="organizationId"
                                collection="kernel.organizations"
                                :extra-fields="['code']"
                                :template="'{{ code }} - {{ name }}'"
                                disabled
                            />
                            <ui-field
                                name="salesManagerId"
                                collection="kernel.partners"
                                :html-template="organizationMemberTemplate"
                                disabled
                            />
                            <ui-field name="salespersonId" collection="kernel.partners" disabled />
                        </el-tab-pane>

                        <el-tab-pane
                            name="exchange-rates"
                            :label="'Exchange Rates' | t"
                            :disabled="!$setting('system.multiCurrency')"
                        >
                            <ui-field
                                name="exchangeRates"
                                class="mb0"
                                :key="model.grandTotal"
                                :min-empty-rows="0"
                                :enable-add-remove="false"
                                :enable-row-handle="false"
                            />
                        </el-tab-pane>
                    </el-tabs>
                </ui-totals>
            </el-tab-pane>

            <el-tab-pane name="campaigns" :label="'Campaigns' | t" :disabled="(model.campaigns || []).length < 1">
                <ui-table
                    :items="model.campaigns || []"
                    :columns="campaignsColumns"
                    :enable-auto-height="true"
                    :enable-selection="false"
                />
            </el-tab-pane>

            <el-tab-pane name="stage-history" :label="'Stage History' | t" :disabled="stages.length < 1">
                <ui-table
                    :items="model.stageHistory || []"
                    :columns="stageHistoryColumns"
                    :enable-auto-height="true"
                    :enable-selection="false"
                />
            </el-tab-pane>

            <el-tab-pane name="competitors" :label="'Competitors' | t">
                <ui-legend title="Competitors" class="mt20 mb0 full-width" />
                <ui-field
                    name="competitorIds"
                    class="mb0"
                    field-type="relation"
                    actions="add,remove,create"
                    collection="crm.competitors"
                    view="crm.customer-relations.competitors"
                    :columns="competitorColumns"
                    :filters="competitorFilters"
                    :auto-height="true"
                    @record-added="handleCompetitorAdd"
                    @record-removed="handleCompetitorRemove"
                    @record-created="handleCompetitorCreate"
                    @selected="handleCompetitorSelect"
                />

                <ui-legend title="Competitor Products" class="mt20 mb0 full-width" v-show="!!selectedCompetitorId" />
                <ui-field
                    name="competitorProductIds"
                    class="mb0"
                    field-type="relation"
                    actions="add,remove,create"
                    collection="crm.competitor-products"
                    view="crm.customer-relations.competitor-products"
                    :columns="competitorProductColumns"
                    :filters="competitorProductFilters"
                    :auto-height="true"
                    @record-added="handleCompetitorProductAdd"
                    @record-removed="handleCompetitorProductRemove"
                    @record-created="handleCompetitorProductCreate"
                    v-show="!!selectedCompetitorId"
                />
            </el-tab-pane>

            <el-tab-pane name="details" :label="'Details' | t">
                <div class="columns mt10">
                    <div class="column is-half">
                        <ui-legend title="General" />
                        <div style="display: flex; max-width: 450px">
                            <ui-field
                                name="financialProjectId"
                                collection="kernel.financial-projects"
                                view="system.management.configuration.financial-projects"
                                disable-detail
                                disable-create
                                :extra-fields="['code']"
                                :template="'{{code}} - {{name}}'"
                                style="flex: 1 1 0"
                            />
                            <el-button
                                class="mb5 ml5"
                                style="flex: 0 0 25px"
                                icon="far fa-check"
                                :title="'Apply To All' | t"
                                :disabled="$params('isPreview')"
                                @click="handleApplyFinancialProject"
                            />
                        </div>
                        <ui-field
                            name="communicationChannelId"
                            collection="kernel.communication-channels"
                            view="system.management.configuration.communication-channels"
                            disable-create
                            disable-detail
                        />
                        <ui-field
                            name="sourceId"
                            collection="sale.sources"
                            view="sale.configuration.sources"
                            disable-create
                            disable-detail
                        />
                        <ui-field
                            name="referencePartnerId"
                            collection="kernel.partners"
                            view="partners.partners"
                            disable-create
                            disable-detail
                        />
                    </div>

                    <div class="column is-half">
                        <ui-legend title="Invoice Address" />
                        <ui-field
                            name="invoiceResponsibleId"
                            collection="kernel.contacts"
                            view="partners.contacts"
                            :filters="{partnerId: model.partnerId, type: 'contact'}"
                            :update-params="updateContactIdParams"
                            :disabled="!model.partnerId"
                            v-show="model.partnerType === 'customer'"
                        />
                        <ui-field
                            name="invoiceAddress"
                            field-type="compact-address"
                            :is-preview="$params('isPreview')"
                            :disabled="!(!!model.partnerId || !!model.leadId)"
                        >
                            <div style="display: flex; max-width: 450px" v-show="model.partnerType === 'customer'">
                                <ui-field
                                    name="invoiceAddressId"
                                    label="hide"
                                    collection="kernel.contacts"
                                    view="partners.contacts"
                                    :filters="{partnerId: model.partnerId, type: 'invoice-address'}"
                                    :update-params="updateInvoiceAddressSelectParams"
                                    :disabled="!model.partnerId"
                                    style="flex: 1 1 0"
                                />
                                <el-button
                                    class="mb5 ml5"
                                    style="flex: 0 0 25px"
                                    icon="far fa-check"
                                    :title="'Apply To All' | t"
                                    :disabled="$params('isPreview')"
                                    @click="handleApplyInvoiceAddress"
                                />
                            </div>
                        </ui-field>
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane name="logistics" :label="'Logistics' | t">
                <div class="columns mt10">
                    <div class="column is-half">
                        <ui-legend title="Delivery Address" />
                        <ui-field
                            name="deliveryReceiverId"
                            collection="kernel.contacts"
                            view="partners.contacts"
                            :filters="{partnerId: model.partnerId, type: 'contact'}"
                            :update-params="updateContactIdParams"
                            :disabled="!model.partnerId"
                            v-show="model.partnerType === 'customer'"
                        />
                        <ui-field
                            name="deliveryAddress"
                            field-type="compact-address"
                            :is-preview="$params('isPreview')"
                            :disabled="!(!!model.partnerId || !!model.leadId)"
                        >
                            <div style="display: flex; max-width: 450px" v-show="model.partnerType === 'customer'">
                                <ui-field
                                    name="deliveryAddressId"
                                    label="hide"
                                    collection="kernel.contacts"
                                    view="partners.contacts"
                                    :filters="{partnerId: model.partnerId, type: 'delivery-address'}"
                                    :update-params="updateDeliveryAddressSelectParams"
                                    :disabled="!model.partnerId"
                                    style="flex: 1 1 0"
                                />
                                <el-button
                                    class="mb5 ml5"
                                    style="flex: 0 0 25px"
                                    icon="far fa-check"
                                    :title="'Apply To All' | t"
                                    :disabled="$params('isPreview')"
                                    @click="handleApplyDeliveryAddress"
                                />
                            </div>
                        </ui-field>
                        <ui-field name="deliveryAddressCode" />
                    </div>

                    <div class="column is-half">
                        <ui-legend title="Delivery Information" />
                        <div style="display: flex; max-width: 450px">
                            <ui-field
                                name="warehouseId"
                                collection="inventory.warehouses"
                                view="inventory.configuration.warehouses"
                                :filters="warehouseIdFilters"
                                :extra-fields="['shortName']"
                                :template="'{{shortName}} - {{name}}'"
                                style="flex: 1 1 0"
                            />
                            <el-button
                                class="mb5 ml5"
                                style="flex: 0 0 25px"
                                icon="far fa-check"
                                :title="'Apply To All' | t"
                                :disabled="$params('isPreview')"
                                @click="handleApplyWarehouse"
                            />
                        </div>
                        <div style="display: flex; max-width: 450px">
                            <ui-field name="scheduledDate" style="flex: 1 1 0" />
                            <el-button
                                class="mb5 ml5"
                                style="flex: 0 0 25px"
                                icon="far fa-check"
                                :title="'Apply To All' | t"
                                :disabled="status === 'payment-planned' || $params('isPreview')"
                                @click="handleApplyScheduledDate"
                            />
                        </div>
                        <ui-field name="deliveryPriority" :options="deliveryPriorityOptions" translate-labels />
                        <ui-field name="deliveryPolicy" :options="deliveryPolicyOptions" translate-labels />
                        <ui-field
                            name="deliveryConditionId"
                            collection="logistics.delivery-conditions"
                            view="logistics.configuration.delivery-conditions"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                        />
                        <ui-field
                            name="deliveryMethodId"
                            collection="logistics.delivery-methods"
                            view="logistics.configuration.delivery-methods"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                        />
                        <ui-field name="deliveryNote" :rows="3" />
                        <ui-field
                            name="imoAndMmsiNo"
                            dont-update-label-cases
                            v-show="deliveryMethodType === 'seaway'"
                        />
                        <ui-field name="shipName" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipRadioCallName" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipRegistrationName" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipNetWeight" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipGrossWeight" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipRequirements" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipPortOfRegistration" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="trainNo" v-show="deliveryMethodType === 'railroad'" />
                        <ui-field name="trainWagonNo" v-show="deliveryMethodType === 'railroad'" />
                        <ui-field name="licensePlateNo" v-show="deliveryMethodType === 'land-route'" />
                        <ui-field name="aircraftNo" v-show="deliveryMethodType === 'airline'" />
                        <ui-field
                            name="carrierId"
                            collection="logistics.carriers"
                            view="logistics.configuration.carriers"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                        />
                        <ui-field name="cargoTrackingCode" />
                        <ui-field name="shippingPaymentType" :options="shippingPaymentTypeOptions" translate-labels />
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane name="financial" :label="'Financial' | t">
                <div class="columns mt10">
                    <div class="column is-half">
                        <ui-legend title="General" />
                        <ui-field
                            name="paymentTermId"
                            collection="finance.payment-terms"
                            view="finance.configuration.payment-terms"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                            :filters="paymentTermIdFilters"
                            :disabled="status === 'payment-planned'"
                        />
                        <ui-field name="paymentPlanningDate" disabled />
                        <ui-field
                            name="guaranteeId"
                            collection="finance.guarantees"
                            view="finance.banking.guarantees.list"
                            :filters="{
                                type: {$in: ['partner-received', 'partner-issued']},
                                partnerId: model.partnerId,
                                status: 'approved'
                            }"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                            :disabled="!model.partnerId"
                            v-show="model.partnerType === 'customer'"
                        />
                        <ui-field
                            name="priceListId"
                            collection="sale.price-lists"
                            :filters="priceListIdFilters"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            :disabled="status === 'payment-planned'"
                            v-show="$setting('sale.salesPriceList')"
                        />
                        <ui-field
                            name="customerPriceListId"
                            collection="sale.customer-price-lists"
                            :filters="customerPriceListIdFilters"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            :disabled="status === 'payment-planned' || !model.partnerId"
                            v-show="$setting('sale.customerPriceList')"
                        />
                    </div>

                    <div class="column is-half">
                        <ui-legend title="Payment Address" />
                        <ui-field
                            name="paymentResponsibleId"
                            collection="kernel.contacts"
                            view="partners.contacts"
                            :filters="{partnerId: model.partnerId, type: 'contact'}"
                            :update-params="updateContactIdParams"
                            :disabled="!model.partnerId"
                        />
                        <ui-field
                            name="paymentAddress"
                            field-type="compact-address"
                            :is-preview="$params('isPreview')"
                            :disabled="!(!!model.partnerId || !!model.leadId)"
                        >
                            <div style="display: flex; max-width: 450px">
                                <ui-field
                                    name="paymentAddressId"
                                    label="hide"
                                    collection="kernel.contacts"
                                    view="partners.contacts"
                                    :filters="{partnerId: model.partnerId, type: 'other-address'}"
                                    :update-params="updatePaymentAddressSelectParams"
                                    :disabled="!model.partnerId"
                                    style="flex: 1 1 0"
                                />
                                <el-button
                                    class="mb5 ml5"
                                    style="flex: 0 0 25px"
                                    icon="far fa-check"
                                    :title="'Apply To All' | t"
                                    :disabled="$params('isPreview')"
                                    @click="handleApplyPaymentAddress"
                                />
                            </div>
                        </ui-field>
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane name="guarantors" :label="'Guarantors' | t">
                <ui-field
                    name="guarantorIds"
                    collection="kernel.contacts"
                    view="partners.contacts.detail"
                    :columns="[
                        {field: 'name', label: 'Name'},
                        {field: 'identity', label: 'Identity no', width: 180},
                        {field: 'phone', label: 'Phone', phoneCell: true, width: 180},
                        {field: 'email', label: 'Email Address', width: 240}
                    ]"
                    :update-params="
                        params => ({...params, model: {type: 'contact'}, forGuarantor: true, hideTypes: true})
                    "
                    field-type="relation"
                    actions="create,delete"
                    :auto-height="true"
                />
            </el-tab-pane>

            <el-tab-pane
                name="discoveries-observations"
                :label="'Discoveries & Observations' | t"
                :disabled="!$setting('crm.isDiscoveriesObservationsEnabled') || !$params('id')"
            >
                <crm-components-discoveries-observations-list
                    document="quotation"
                    :id="$params('id')"
                    v-if="activeTab === 'discoveries-observations'"
                />
            </el-tab-pane>

            <el-tab-pane
                name="additional-information"
                :label="$t('Additional Information')"
                v-if="showAdditionalInformationTab"
            >
                <system-components-additional-information
                    ref="additionalInformationForm"
                    type="sale-quotation"
                    :wrapper-style="{padding: '20px 0px 0px 0px !important'}"
                    :payload="model.additionalInformation || {}"
                    :is-preview="$params('isPreview')"
                />
            </el-tab-pane>

            <el-tab-pane name="note" :label="'Note' | t">
                <ui-field name="content" class="full-width" label="hide" field-type="rich-editor" />
            </el-tab-pane>

            <el-tab-pane name="attachments" :label="'Attachments' | t">
                <ui-field name="attachments" field-type="attachments" auto-height />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import data from './detail/data';
import computed from './detail/computed';
import watch from './detail/watch';
import methods from './detail/methods';
import created from './detail/created';
import mounted from './detail/mounted';
import beforeDestroy from './detail/before-destroy';

import LimitProgressIndicator from '../orders/detail/_limit-progress';

export default {
    mixins: [data, computed, watch, methods, created, mounted, beforeDestroy],

    components: {
        LimitProgressIndicator
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.quotation-validity-status-container {
    padding-left: 30px;
    display: flex;
    align-items: center;
}

.quotation-validity-status {
    display: flex;
    align-items: center;
    height: 22px;
    padding: 0 15px;
    border-radius: 11px;
    background-color: $danger;
    color: white;
    font-size: 10px;
    font-weight: bold;

    i {
        display: block;
        margin-right: 7px;
    }

    &.is-valid {
        background-color: $success;
    }

    &.is-expired {
        background-color: $danger;
    }
}

.sale-quotations-detail-form {
    .ui-form.has-form-top {
        padding-top: 130px;
    }

    &.no-stages .ui-form.has-form-top {
        padding-top: 60px;
    }

    .quotation-header {
        position: absolute;
        display: flex;
        flex-flow: row nowrap;
        top: 40px;
        left: 0;
        width: 100%;
        height: 65px;
        padding-top: 10px;
        border-bottom: 1px solid $border-color-light;
        background-color: #fff;
        overflow: hidden;

        .quotation-steps {
            flex: 1 1 0;

            .el-step__icon {
                width: 20px;
                height: 20px;
            }

            .el-step.is-horizontal .el-step__line {
                height: 1px;
                top: 10px;
            }

            .el-step__head {
                height: 20px !important;
            }

            .el-step__head.is-process {
                color: $primary;
                border-color: $primary;

                .el-step__icon {
                    position: relative;

                    &:after {
                        position: absolute;
                        left: 3px;
                        top: 3px;
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        background-color: $primary;
                        content: ' ';
                    }
                }
            }

            .el-step__head:not(.is-success) {
                .el-step__icon-inner {
                    display: none;
                }
            }

            .el-step__head.is-wait {
                .el-step__icon {
                    border-width: 1px;
                }
            }

            .el-step__title {
                font-size: 10px !important;
                line-height: 28px !important;

                &.is-process {
                    font-weight: $font-weight;
                    color: $primary;
                }
            }

            .el-step__description {
                display: none;
            }

            .el-step__icon-inner {
                font-size: 12px;
            }

            &:not(.is-disabled) .el-step__head:not(.is-process),
            &:not(.is-disabled) .el-step__title:not(.is-process) {
                cursor: pointer;
            }
        }

        .quotation-probability {
            position: relative;
            flex: 0 0 80px;
            padding: 2px 20px 0 20px;

            &:before {
                position: absolute;
                right: 0;
                top: 0;
                width: 1px;
                height: 45px;
                background-color: $border-color-light;
                content: ' ';
            }

            .el-progress--circle .el-progress__text {
                font-size: 9px !important;
            }
        }

        .quotation-amount {
            position: relative;
            display: flex;
            justify-content: flex-end;
            padding: 3px 20px 0 20px;

            &:before {
                position: absolute;
                left: 0;
                top: 0;
                width: 1px;
                height: 45px;
                background-color: $border-color-light;
                content: ' ';
            }

            .top-report {
                line-height: 1;

                .report-label {
                    margin-bottom: 6px;
                    font-size: 12px;
                    color: $text-color-lighter;
                }

                .report-value {
                    font-size: 21px;
                }
            }
        }
    }
}
</style>
