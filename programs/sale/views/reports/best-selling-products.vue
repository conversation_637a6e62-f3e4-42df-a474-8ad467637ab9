<template>
    <ui-view
        type="content"
        :title="'Best Selling Products' | t"
        :left-panel-width="270"
        class="sale-reports-best-selling-products"
        v-if="initialized"
    >
        <template slot="left-panel" v-if="productIds.length < 1 && module !== 'ecommerce'">
            <div class="sale-reports-best-selling-products-categories">
                <div class="category-search">
                    <el-input
                        v-model="categorySearchQuery"
                        :placeholder="'Search category..' | t"
                        prefix-icon="el-icon-search"
                        autocorrect="off"
                        autocapitalize="off"
                        spellcheck="false"
                        clearable
                        size="medium"
                        @input="categorySearchQuery = $event"
                    />
                </div>
                <ui-table
                    ref="table"
                    :items="categories"
                    :columns="categoriesColumns"
                    :search="categorySearchQuery"
                    :enable-row-handle="false"
                    no-zebra
                    :options="categoriesTableOptions"
                    @selected="handleSelectCategory"
                    v-if="categories.length > 0"
                />
            </div>
        </template>

        <template slot="top-panel">
            <ui-scope
                ref="scope"
                id="sale.reports.best-selling-products"
                :applied-items="appliedScopeItems"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />

            <div class="custom-filters">
                <el-select v-model="selectedCurrency" filterable>
                    <el-option
                        v-for="option in currencyOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>

                <el-select v-model="preferredUnit" filterable clearable>
                    <el-option
                        v-for="option in preferredUnitOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>
            </div>
        </template>

        <ui-table
            ref="table"
            :key="tableKey"
            :id="`sale.reports.best-selling-products-${selectedCurrency}-${preferredUnit}`"
            row-model="serverSide"
            :columns="columns"
            :filters="filters"
            :get-rows="getRows"
            :summary-row="summaryRow"
            :enable-sorting="false"
            :enable-selection="false"
        />
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {deepDiff, escapeRegExp} from 'framework/helpers';

export default {
    data: () => ({
        payload: {
            type: 'order',
            startDate: null,
            endDate: null,
            query: {}
        },
        selectedCurrency: '',
        preferredUnit: '',
        units: [],
        currencies: [],
        refreshingForRealTime: false,
        rowCount: null,
        loadKey: _.uniqueId('loadKey_'),
        initialized: false,
        categorySearchQuery: '',
        module: '',
        categories: [],
        categoriesColumns: [
            {
                field: 'name',
                label: 'Category',
                cellRenderer: 'agGroupCellRenderer',
                showRowGroup: true,
                suppressMenu: true,
                suppressMovable: true,
                cellRendererParams: {
                    suppressCount: true,
                    suppressDoubleClickExpand: true,
                    innerRenderer(params) {
                        const data = params.data;

                        if (_.isObject(data)) {
                            return data.name;
                        }
                    }
                },
                getQuickFilterText(params) {
                    const data = params.data;

                    if (_.isObject(data)) {
                        return `${data.code} - ${data.name}`;
                    }
                }
            }
        ],
        categoriesTableOptions: {
            treeData: true,
            groupDefaultExpanded: -1,
            groupSuppressAutoColumn: true,
            getDataPath(data) {
                return data.tree.path.split('/');
            }
        },
        productIds: [],
        categoryFilters: {}
    }),

    computed: {
        tableKey() {
            return this.selectedCurrency + this.preferredUnit + this.loadKey;
        },
        filters() {
            return {};
        },
        currencyOptions() {
            return this.currencies.map(currency => ({
                value: currency.name,
                label: currency.name
            }));
        },
        preferredUnitOptions() {
            return this.units.map(unit => ({
                value: unit._id,
                label: unit.name
            }));
        },
        columns() {
            const company = this.$store.getters['session/company'];
            const self = this;

            return [
                {
                    field: 'productCode',
                    label: 'Product code',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.productId,
                                view: 'inventory.catalog.products'
                            };
                        }
                    },
                    width: 150
                },
                {
                    field: 'productDefinition',
                    label: 'Product definition',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.productId,
                            view: 'inventory.catalog.products-detail'
                        };
                    },
                    minWidth: 150
                },
                {
                    field: 'unit.name',
                    label: 'Unit',
                    width: 150
                },
                {
                    field: 'productType',
                    label: 'Product type',
                    visible: false,
                    width: 150,
                    valueLabels: [
                        {value: 'stockable', label: 'Stockable product'},
                        {value: 'service', label: 'Service product'}
                    ],
                    translateLabels: true
                },
                {
                    field: 'total',
                    label: 'Total',
                    width: 150,
                    format: 'currency'
                },
                {
                    field: 'totalFC',
                    label: 'Total (FC)',
                    width: 150,
                    format: 'currency',
                    hidden: self.selectedCurrency === company.currency.name,
                    formatOptions() {
                        const currency = self.currencies.find(c => c.name === self.selectedCurrency);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    }
                },
                {
                    field: 'averageUnitPrice',
                    label: 'Average unit price',
                    width: 150,
                    format: 'unit-price'
                },
                {
                    field: 'totalQuantity',
                    label: 'Quantity',
                    width: 150,
                    format: 'decimal'
                }
            ];
        },
        appliedScopeItems() {
            const self = this;

            return self.module !== 'ecommerce'
                ? [
                      {
                          type: 'filter',
                          payload: {
                              field: 'type',
                              label: 'Type',
                              translateLabels: true,
                              singleSelect: true,
                              isApplied: true,
                              isRemovable: false,
                              value: {value: 'order', label: this.$t('Order')},
                              items: [
                                  {value: 'invoice', label: 'Invoice'},
                                  {value: 'order', label: 'Order'},
                                  {value: 'quotation', label: 'Quotation'}
                              ]
                          }
                      }
                  ]
                : [];
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                {
                    field: 'isNotReturn',
                    label: 'Normal invoices',
                    query: {isReturn: {$ne: true}},
                    condition() {
                        return self.module !== 'ecommerce';
                    }
                },
                {
                    field: 'isReturn',
                    label: 'Return invoices',
                    query: {isReturn: true},
                    condition() {
                        return self.module !== 'ecommerce';
                    }
                },
                {
                    field: 'date',
                    code: 'date',
                    label: 'Date',
                    type: 'date',
                    operator: 'in-range',
                    allowedOperators: ['in-range']
                },
                {
                    field: 'partnerId',
                    label: 'Customer',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'customer', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'documentTypeId',
                    label: 'Document type',
                    collection: 'sale.document-types',
                    filters: {
                        $sort: {name: 1}
                    }
                },
                {
                    field: 'invoiceType',
                    label: 'Invoice type',
                    translateLabels: true,
                    items: [
                        {value: 'sale', label: 'Sale'},
                        {value: 'exchange-difference', label: 'Exchange difference'},
                        {value: 'due-difference', label: 'Due difference'},
                        {value: 'price-difference', label: 'Price difference'},
                        {value: 'discount', label: 'Discount'}
                    ],
                    condition() {
                        return self.module !== 'ecommerce';
                    }
                }
            ];
        }
    },

    methods: {
        async getRows(query, params) {
            if (!!this.isFiltering) {
                this.$params('loading', true);
            }

            const startRow = params.request.startRow;
            const endRow = params.request.endRow;
            const result = await this.$rpc('sale.get-best-selling-products-report', {
                ...this.payload,
                preferredUnit: this.preferredUnit,
                selectedCurrency: this.selectedCurrency,
                categoryFilters: this.categoryFilters,
                module: this.module,
                limit: endRow - startRow,
                skip: startRow
            });

            if (!!this.isFiltering) {
                this.isFiltering = false;
                this.$params('loading', false);
            }

            this.rowCount = result.total;

            return result;
        },
        async summaryRow() {
            const result = await this.$rpc('sale.get-total-best-selling-products-report', {
                ...this.payload,
                preferredUnit: this.preferredUnit,
                selectedCurrency: this.selectedCurrency,
                categoryFilters: this.categoryFilters,
                module: this.module
            });

            return {
                rowCount: this.rowCount,
                productDefinition: this.$t('TOTAL'),
                total: result.total,
                totalFC: result.totalFC,
                averageUnitPrice: result.totalQuantity > 0 ? result.total / result.totalQuantity : 0,
                totalQuantity: result.totalQuantity
            };
        },
        handleScopeChange(model) {
            const payload = _.cloneDeep(this.payload);
            const query = model.query;

            // Reset.
            payload.partnerId = [];
            payload.startDate = null;
            payload.endDate = null;
            payload.branchId = [];
            payload.status = [];

            // Get type.
            if (_.isString(query.type)) {
                payload.type = query.type;

                delete query.type;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isString(q.type)) {
                        payload.type = q.type;
                    }
                });

                query.$and = query.$and.filter(q => !_.isString(q.type));
            }

            // Start and end date.
            if (_.isObject(query.date)) {
                payload.startDate = query.date.$gte;
                payload.endDate = query.date.$lte;

                delete query.date;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isObject(q.date)) {
                        payload.startDate = q.date.$gte;
                        payload.endDate = q.date.$lte;
                    }
                });

                query.$and = query.$and.filter(q => !_.isObject(q.date));
            }

            // Get partner ids.
            if (_.isObject(query.partnerId) && Array.isArray(query.partnerId.$in)) {
                payload.partnerId = query.partnerId.$in;

                delete query.partnerId;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isObject(q.partnerId) && Array.isArray(q.partnerId.$in)) {
                        payload.partnerId = q.partnerId.$in;
                    }
                });

                query.$and = query.$and.filter(q => !_.isObject(q.partnerId));
            }

            // Get branch ids.
            if (_.isObject(query.branchId) && Array.isArray(query.branchId.$in)) {
                payload.branchId = query.branchId.$in;

                delete query.branchId;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isObject(q.branchId) && Array.isArray(q.branchId.$in)) {
                        payload.branchId = q.branchId.$in;
                    }
                });

                query.$and = query.$and.filter(q => !_.isObject(q.branchId));
            }

            // Get status.
            if (_.isObject(query.status) && Array.isArray(query.status.$in)) {
                payload.status = query.status.$in;

                delete query.status;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isObject(q.status) && Array.isArray(q.status.$in)) {
                        payload.status = q.status.$in;
                    }
                });

                query.$and = query.$and.filter(q => !_.isObject(q.status));
            }

            // Fix query.
            if (Array.isArray(query.$and) && query.$and.length === 0) {
                delete query.$and;
            }

            // Get query
            payload.query = {...query, ..._.cloneDeep(this.$params('filters') || {})};

            if (!_.isUndefined(deepDiff(_.cloneDeep(this.payload), payload))) {
                this.payload = payload;
                this.isFiltering = true;
                this.loadKey = _.uniqueId('loadKey_');
            }
        },
        refresh() {
            const table = this.$refs.table;
            if (_.isObject(table)) {
                table.refreshData();
            }
        },
        handleSelectCategory(selected) {
            const query = {$or: []};

            for (const category of selected) {
                query.$or.push({
                    'items.productCategoryPath': {
                        $regex: `^${escapeRegExp(category.path)}/`,
                        $options: 'i'
                    }
                });
                query.$or.push({
                    'items.productCategoryPath': {
                        $regex: `^${escapeRegExp(category.path)}$`,
                        $options: 'i'
                    }
                });
            }

            if (query.$or.length < 1) {
                delete query.$or;
            }

            this.loadKey = _.uniqueId('loadKey_');
            this.categoryFilters = query;
        }
    },

    async created() {
        const company = this.$store.getters['session/company'];

        this.refreshForRealTime = _.debounce(this.refresh, 300, {leading: false, trailing: true});

        this.categories = await this.$collection('inventory.product-categories').find();

        this.currencies = await this.$collection('kernel.currencies').find({
            name: {$in: ['TL', 'USD', 'EUR', 'GBP', 'RUB']}
        });
        this.units = await this.$collection('kernel.units').find({
            $select: ['name']
        });
        this.selectedCurrency = company.currency.name;

        this.module = (this.$params('filters') || {}).module;

        this.$collection('accounting.customer-invoices').on('all', this.refreshForRealTime);
        this.$collection('sale.orders').on('all', this.refreshForRealTime);
        this.$collection('sale.quotations').on('all', this.refreshForRealTime);

        this.initialized = true;
    },

    beforeDestroy() {
        this.$collection('accounting.customer-invoices').removeListener('all', this.refreshForRealTime);
        this.$collection('sale.orders').removeListener('all', this.refreshForRealTime);
        this.$collection('sale.quotations').removeListener('all', this.refreshForRealTime);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.sale-reports-best-selling-products {
    .ui-view-top-panel {
        display: flex;

        .custom-filters {
            display: flex;
            flex-flow: nowrap;
            width: 240px;
            justify-content: center;
            align-items: flex-end;
            column-gap: 10px;
        }
    }

    .sale-reports-best-selling-products-categories {
        position: relative;
        display: flex;
        flex-flow: column nowrap;
        flex: 0 0 300px;
        min-width: 210px;
        height: 100%;
        background-color: #fff;

        .category-search {
            width: 100%;
            flex: 0 0 30px;

            .el-input__inner {
                border-radius: 0;
                border: none;
                border-bottom: 1px solid $border-color;
            }
        }

        .ag-pinned-left-cols-container,
        .ag-pinned-left-header,
        .ag-header-viewport,
        .ag-pinned-right-header,
        .ag-header,
        .ag-header-row {
            display: none !important;
        }
    }
}
</style>
