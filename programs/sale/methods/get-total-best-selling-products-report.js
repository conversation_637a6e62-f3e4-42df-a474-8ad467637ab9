import _ from 'lodash';

export default {
    name: 'get-total-best-selling-products-report',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user.
        if (!_.isObject(user)) {
            throw new Error('User must be provided!');
        }

        // Get type.
        const type = payload.type || 'invoice';

        // Get collection.
        let collectionName = null;
        if (type === 'invoice') collectionName = 'accounting.customer-invoices';
        else if (type === 'order') collectionName = 'sale.orders';
        else if (type === 'quotation') collectionName = 'sale.quotations';
        const collection = app.collection(collectionName);

        // Get start and end date.
        let endDate = app.datetime.local().toJSDate();
        let startDate = app.datetime.fromJSDate(endDate).minus({years: 1}).toJSDate();
        if (_.isDate(payload.endDate)) endDate = payload.endDate;
        if (_.isDate(payload.startDate)) startDate = payload.startDate;

        // Get date field.
        let dateField = 'issueDate';
        if (type === 'order') dateField = 'orderDate';
        else if (type === 'quotation') dateField = 'quotationDate';

        // Get partner ids.
        let partnerIds = [];
        if (_.isString(payload.partnerId)) partnerIds = [payload.partnerId];
        if (Array.isArray(payload.partnerId)) partnerIds = payload.partnerId;

        // Get branch ids.
        let branchIds = [];
        if (_.isString(payload.branchId)) branchIds = [payload.branchId];
        if (Array.isArray(payload.branchId)) branchIds = payload.branchId;
        if (!(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) && branchIds.length < 1) {
            branchIds = user.branchIds;
        }

        // Get query.
        let query = {};
        if (_.isObject(payload.query)) query = payload.query;

        // Prepare status filter.
        let statusQuery = {};
        if (Array.isArray(payload.status) && payload.status.length > 0) {
            statusQuery = {status: {$in: payload.status}};
        } else if (_.isString(payload.status) && payload.status) {
            statusQuery = {status: payload.status};
        } else {
            if (type === 'invoice') {
                statusQuery = {status: 'approved'};
            } else if (type === 'order') {
                statusQuery = {
                    status: {$in: ['approved', 'to-invoice', 'invoiced']}
                };
            } else if (type === 'quotation') {
                statusQuery = {status: 'won'};
            }
        }

        // Prepare match state.
        const $match = {
            ...query,
            ...statusQuery,
            ...(payload.module === 'ecommerce' ? {module: 'ecommerce'} : {}),
            [dateField]: {$gte: startDate, $lte: endDate}
        };
        if (partnerIds.length > 0) {
            $match.partnerId = {$in: partnerIds};
        }
        if (branchIds.length > 0) {
            $match.branchId = {$in: branchIds};
        }

        // Check permission.
        if (!user.isRoot) {
            const permission = (user.recordPermissions || []).find(p => p.name === collectionName);

            if (!_.isObject(permission)) {
                return {total: 0, totalFC: 0, totalQuantity: 0};
            }

            if (permission.read === 'no') {
                return {total: 0, totalFC: 0, totalQuantity: 0};
            } else if (permission.read === 'owned') {
                if (!Array.isArray($match.$and)) $match.$and = [];

                $match.$and.push({createdBy: user._id});
            }
        }

        // Prepare unwind stage.
        const $unwind = '$items';

        // Get pipeline.
        const pipeline = [
            {$match},
            {$project: {items: 1, exchangeRatesMap: 1, currencyRate: 1}},
            {$unwind},
            {
                $project: {
                    productId: '$items.productId',
                    baseUnitId: '$items.baseUnitId',
                    baseQuantity: '$items.baseQuantity',
                    total: {$multiply: ['$currencyRate', '$items.total']},
                    totalFC: {
                        $divide: [
                            {$multiply: ['$currencyRate', '$items.total']},
                            `$exchangeRatesMap.${payload.selectedCurrency}`
                        ]
                    },
                    productCategoryPath: '$items.productCategoryPath'
                }
            }
        ];

        if (
            _.isPlainObject(payload.categoryFilters) &&
            Array.isArray(payload.categoryFilters.$or) &&
            payload.categoryFilters.$or.length > 0
        ) {
            const categoryMatch = {
                $match: {
                    $or: payload.categoryFilters.$or
                        .map(filter => {
                            if (!filter['items.productCategoryPath']) return {};

                            const regexPattern = filter['items.productCategoryPath'].$regex;
                            const regexOptions = filter['items.productCategoryPath'].$options || '';

                            return {
                                productCategoryPath: {
                                    $regex: regexPattern,
                                    $options: regexOptions
                                }
                            };
                        })
                        .filter(item => Object.keys(item).length > 0)
                }
            };

            if (categoryMatch.$match.$or.length > 0) {
                pipeline.push(categoryMatch);
            }
        }

        pipeline.push({
            $group: {
                _id: null,
                totalQuantity: {$sum: '$baseQuantity'},
                total: {$sum: '$total'},
                totalFC: {$sum: '$totalFC'}
            }
        });

        pipeline.push({
            $project: {
                _id: 0,
                totalQuantity: 1,
                total: 1,
                totalFC: 1
            }
        });

        // Get report.
        const report = await collection.aggregate(pipeline);
        let result = {total: 0, totalFC: 0, totalQuantity: 0};

        if (report.length > 0) {
            result = {
                total: app.round(report[0].total || 0, 'total'),
                totalFC: app.round(report[0].totalFC || 0, 'total'),
                totalQuantity: app.round(report[0].totalQuantity || 0, 'total')
            };
        }

        // Handle preferred unit conversion
        if (payload.preferredUnit && result.totalQuantity > 0) {
            // Get units.
            const units = _.orderBy(
                await app.collection('kernel.units').find({
                    $select: ['name', 'category', 'ratio', 'type'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                }),
                ['order'],
                'asc'
            );

            const targetUnit = units.find(unit => unit._id === payload.preferredUnit);

            if (targetUnit) {
                result.totalQuantity = result.totalQuantity;
            }
        }

        return result;
    }
};
