import _ from 'lodash';

export default {
    name: 'close-quotation',
    async action(payload, params) {
        const app = this.app;
        const competitorsCollection = app.collection('crm.competitors');
        const quotationsCollection = app.collection('sale.quotations');
        const ordersCollection = app.collection('sale.orders');
        const invoicesCollection = app.collection('accounting.customer-invoices');
        const company = await this.app.collection('kernel.company').findOne({});
        let quotation = payload.quotation;

        // Check active quotation conversion.
        if (await app.cache.has(`sale.quotations-conversions-${quotation._id}`)) {
            throw new Error('Transaction blocked for 30 seconds due to suspicious operation!!!');
        } else {
            await app.cache.set(`sale.quotations-conversions-${quotation._id}`, true, 30);
        }

        if (
            typeof quotation.isExpired === 'boolean' &&
            quotation.isExpired !== false &&
            app.setting('sale.forceQuotationValidity') &&
            payload.type !== 'lost'
        ) {
            const originalQuotation = await quotationsCollection.findOne({
                _id: quotation._id,
                $select: ['expiryDate']
            });
            const expiryDate = app.datetime.fromJSDate(originalQuotation.expiryDate);
            const now = app.datetime.local();
            if (now.diff(expiryDate).as('days') > 0) {
                throw new app.errors.Unprocessable(
                    this.translate('The quotation cannot be saved because it has expired!')
                );
            }
        }

        // Conversion.
        let conversion = await app.collection('sale.conversions').findOne({
            quotationId: quotation._id
        });
        if (!_.isPlainObject(conversion)) {
            conversion = {};

            conversion.quotationId = quotation._id;
            conversion.quotationCode = quotation.code;
            conversion.quotationRecordDate = quotation.recordDate;
            conversion.status = 'quotation';
            conversion = await app.collection('sale.conversions').create(conversion);
        }

        // Update conversion.
        conversion.branchId = quotation.branchId;
        conversion.organizationId = quotation.organizationId;
        conversion.salesManagerId = quotation.salesManagerId;
        conversion.salespersonId = quotation.salespersonId;
        conversion.sourceId = quotation.sourceId;
        conversion.communicationChannelId = quotation.communicationChannelId;
        if (quotation.partnerType === 'customer') {
            const p = await app.collection('kernel.partners').findOne({
                _id: quotation.partnerId,
                $select: ['_id', 'code', 'createdAt']
            });

            conversion.customerId = p._id;
            conversion.customerCode = p.code;
            conversion.customerRecordDate = p.createdAt;
        } else if (quotation.partnerType === 'lead') {
            const lead = await app.collection('crm.leads').findOne({
                _id: quotation.leadId,
                $select: ['_id', 'code', 'recordDate']
            });

            conversion.leadId = lead._id;
            conversion.leadCode = lead.code;
            conversion.leadRecordDate = lead.recordDate;
        }
        conversion.date = app.datetime.local().toJSDate();

        if (payload.type === 'won') {
            const documentType = await app.collection('sale.document-types').findOne({
                _id: quotation.documentTypeId,
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            let partner = null;
            let contactPersonId = null;
            let organizationSettings = {};

            // Get organization settings.
            if (quotation.organizationId) {
                const organization = await app.collection('kernel.organizations').get(quotation.organizationId);
                const team = organization.team || [];
                const currentMember = team.find(m => m.partnerId === quotation.salespersonId);
                organizationSettings = organization.settings || {};

                if (
                    _.isObject(currentMember) &&
                    _.isObject(currentMember.settings) &&
                    !_.isEmpty(currentMember.settings)
                ) {
                    organizationSettings = _.assign(organizationSettings, currentMember.settings);
                }
            }

            if (quotation.partnerType === 'lead') {
                const lead = await app.collection('crm.leads').get(quotation.leadId, {user: params.user});

                const numbering = await app.collection('kernel.numbering').findOne(
                    {
                        code: 'partnerCustomerNumbering',
                        $select: ['_id']
                    },
                    {
                        disableInUseCheck: true,
                        disableActiveCheck: true,
                        disableSoftDelete: true
                    }
                );
                const group = payload.groupId
                    ? {_id: payload.groupId}
                    : await app.collection('kernel.partner-groups').findOne({
                          code: 'general-customer',
                          $select: ['_id']
                      });
                const p = {};

                p.type = 'customer';
                p.code = await app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    save: true
                });
                p.name = lead.name;
                p.isCompany = lead.isCompany;
                p.email = lead.email;
                p.gender = lead.gender;
                p.website = lead.website;
                p.currencyId = lead.currencyId;
                p.languageId = lead.languageId;
                p.groupId = group._id;
                p.timezone = app.config('app.timezone');
                p.identity = payload.identity;
                p.tin = payload.tin;
                p.branchIds = [lead.branchId];
                p.sectorId = lead.sectorId;
                p.annualIncome = lead.annualIncome;
                p.numberOfEmployees = lead.numberOfEmployees;
                p.phone = lead.phone;
                p.phoneNumbers = lead.phoneNumbers;
                p.address = lead.address;
                p.salesOrganizationId = lead.organizationId;
                p.salesManagerId = lead.salesManagerId;
                p.salespersonId = lead.salespersonId;
                p.countryId = p.address.countryId || company.address.countryId;
                p.accountingAccountId =
                    p.countryId !== company.address.countryId
                        ? app.defaultAccountingAccount('foreignAccountsReceivableAccount', 'sale')
                        : app.defaultAccountingAccount('domesticAccountsReceivableAccount', 'sale');
                p.note = lead.note;
                p.invoiceScenario = app.setting('eops.isEInvoiceActivated')
                    ? 'basic-invoice'
                    : app.setting('eops.isEArchiveInvoiceActivated')
                    ? 'e-archive-invoice'
                    : 'normal';

                if (p.invoiceScenario !== 'normal') {
                    const eInvoiceTypes = await app.collection('eops.e-invoice-types').find({
                        scenarios: p.invoiceScenario,
                        $select: ['_id']
                    });
                    if (eInvoiceTypes.length > 0) {
                        p.eInvoiceTypeId = eInvoiceTypes[0]._id;
                    }
                }

                if (!_.isEmpty(organizationSettings)) {
                    const settings = organizationSettings;

                    if (settings.defaultCustomerGroupId) p.partnerGroupId = settings.defaultCustomerGroupId;
                }

                if (lead.contactPerson) {
                    const contactPerson = {};

                    contactPerson.type = 'contact';
                    contactPerson.name = lead.contactPerson;
                    contactPerson.email = lead.email;
                    contactPerson.phone = lead.phone;
                    contactPerson.website = lead.website;
                    contactPerson.gender = lead.gender;
                    contactPerson.phoneNumbers = lead.phoneNumbers;
                    contactPerson.address = lead.address;

                    const contact = await app.collection('kernel.contacts').create(contactPerson, {
                        user: params.user,
                        userLocation: params.userLocation,
                        setLocation: true
                    });
                    p.contactIds = [contact._id];
                    contactPersonId = contact._id;
                }

                // Default limit definition.
                const pld = await app.collection('finance.partner-limit-definitions').findOne({
                    partnerType: p.type,
                    $or: [
                        {partnerGroupId: {$exists: false}},
                        {partnerGroupId: {$eq: null}},
                        {partnerGroupId: {$eq: ''}},
                        {partnerGroupId: p.groupId}
                    ]
                });
                if (_.isPlainObject(pld)) {
                    p.enableLimitChecks = pld.enableLimitChecks;
                    p.limitControlDocument = pld.limitControlDocument;
                    p.limitOrderControl = pld.limitOrderControl;
                    p.limitQuotationControl = pld.limitQuotationControl;
                    p.limitInvoiceControl = pld.limitInvoiceControl;
                    p.openAccountLimit = pld.openAccountLimit;
                    p.totalLimit = pld.openAccountLimit;
                }

                partner = await app.collection('kernel.partners').create(p, {
                    user: params.user,
                    userLocation: params.userLocation,
                    setLocation: true
                });

                // Update conversion.
                conversion.customerId = partner._id;
                conversion.customerCode = partner.code;
                conversion.customerRecordDate = partner.createdAt;
                conversion.leadToCustomer = true;
                conversion.date = app.datetime.local().toJSDate();
                conversion.status = 'customer';
            } else {
                partner = await app.collection('kernel.partners').get(quotation.partnerId, {user: params.user});

                // Update conversion.
                conversion.customerId = partner._id;
                conversion.customerCode = partner.code;
                conversion.customerRecordDate = partner.createdAt;
                conversion.date = app.datetime.local().toJSDate();
            }

            // Dynamic delivery note.
            quotation.items = (quotation.items || []).map(i => {
                if (
                    _.isPlainObject(i.additionalItemFields) &&
                    !_.isEmpty(i.additionalItemFields) &&
                    !!documentType &&
                    Array.isArray(documentType.additionalItemFields)
                ) {
                    const additionalFields = [];

                    for (const fieldDefinition of documentType.additionalItemFields) {
                        const value = i.additionalItemFields[fieldDefinition.code] || '';
                        const field = {};

                        if (!fieldDefinition.visibleInPrintouts) {
                            continue;
                        }

                        field.code = fieldDefinition.code;
                        field.label = fieldDefinition.label;
                        field.subLabel = fieldDefinition.subLabel;
                        field.value = value;

                        if (field.type === 'integer') {
                            field.value = app.format(value, 'integer');
                        } else if (field.type === 'decimal') {
                            field.value = app.format(value, 'amount');
                        } else if (field.type === 'money') {
                            field.value = app.format(value, 'currency');
                        } else if (field.type === 'date') {
                            field.value = app.format(value, 'date');
                        } else if (field.type === 'datetime') {
                            field.value = app.format(value, 'datetime');
                        } else if (field.type === 'boolean') {
                            field.value = !!value ? app.translate('Yes') : app.translate('No');
                        }

                        additionalFields.push(field);
                    }

                    if (additionalFields.length > 0) {
                        i.deliveryNote = additionalFields.map(field => `${field.label}: ${field.value}`).join(', ');
                    }
                }

                return i;
            });

            // Check address.
            if (
                !_.isEmpty(partner.address) &&
                (!_.isPlainObject(quotation.invoiceAddress) || !quotation.invoiceAddress.city)
            ) {
                quotation.invoiceAddress = partner.address;
            }

            // Copyable items
            try {
                const unitIds = [];
                for (const item of quotation.items ?? []) {
                    if (!unitIds.includes(item.unitId)) {
                        unitIds.push(item.unitId);
                    }
                    if (!unitIds.includes(item.baseUnitId)) {
                        unitIds.push(item.baseUnitId);
                    }
                }
                const units = await app.collection('kernel.units').find({
                    _id: {$in: unitIds},
                    $select: ['_id', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const unitsMap = {};
                for (const unit of units) {
                    unitsMap[unit._id] = unit;
                }
                const copyableItems = [];

                for (const item of quotation.items ?? []) {
                    if (!(item.quantity > 0)) {
                        continue;
                    }

                    const unit = unitsMap[item.unitId];
                    const baseUnit = unitsMap[item.baseUnitId];
                    const copyableItem = {};
                    copyableItem.type = 'sale-quotation';
                    copyableItem.documentId = quotation._id;
                    copyableItem.documentCode = quotation.code;
                    copyableItem.documentCollection = 'sale.quotations';
                    copyableItem.documentView = 'sale.sales.quotations-detail';
                    copyableItem.documentTitle = 'Sales Quotations';
                    copyableItem.partnerId = quotation.partnerId;
                    copyableItem.partnerCode = partner.code;
                    copyableItem.partnerName = partner.name;
                    copyableItem.productId = item.productId;
                    copyableItem.productCode = item.productCode;
                    copyableItem.productDefinition = item.productDefinition;
                    copyableItem.barcode = item.barcode ?? '';
                    copyableItem.unitId = item.unitId;
                    copyableItem.unitName = (unit ?? {}).name;
                    copyableItem.baseUnitId = item.baseUnitId;
                    copyableItem.baseUnitName = (baseUnit ?? {}).name;
                    copyableItem.quantity = item.quantity;
                    copyableItem.remainingQuantity = item.quantity;
                    copyableItem.unitRatio = item.baseQuantity > 0 ? item.quantity / item.baseQuantity : 1;
                    copyableItem.branchId = quotation.branchId;
                    copyableItem.currencyId = quotation.currencyId;
                    copyableItem.currencyRate = quotation.currencyRate ?? 1;
                    copyableItem.unitPrice = item.unitPrice;
                    copyableItem.discount = item.discount;
                    copyableItem.taxIds = [item.taxId];
                    if (
                        typeof item.taxPayload === 'object' &&
                        item.taxPayload !== null &&
                        Array.isArray(item.taxPayload.taxIds) &&
                        item.taxPayload.taxIds.length > 0
                    ) {
                        copyableItem.taxIds = item.taxPayload.taxIds;
                    }
                    copyableItem.issueDate = quotation.quotationDate;
                    copyableItem.dueDate = app.datetime.local().toJSDate();
                    if (
                        _.isObject(quotation.paymentPlan) &&
                        _.isObject(quotation.paymentPlan.report) &&
                        _.isDate(quotation.paymentPlan.report.dueDate)
                    ) {
                        copyableItem.dueDate = quotation.paymentPlan.report.dueDate;
                    }

                    copyableItems.push(copyableItem);
                }

                if (copyableItems.length > 0) {
                    await app.collection('kernel.copyable-items').create(copyableItems);
                }
            } catch (error) {}

            if (payload.conversionType === 'to-order') {
                const numbering = await app.collection('kernel.numbering').findOne(
                    {
                        code: 'salesOrderNumbering',
                        $select: ['_id']
                    },
                    {
                        disableInUseCheck: true,
                        disableActiveCheck: true,
                        disableSoftDelete: true
                    }
                );

                // Create order.
                const o = _.omit(quotation, [
                    'status',
                    'partnerType',
                    'partnerId',
                    'leadId',
                    'stageId',
                    'probability',
                    'quotationDate',
                    'expiryDate',
                    'lostReasonId',
                    'stageHistory',
                    'competitors',
                    'relatedDocuments',
                    'revisionId',
                    'revisionName',
                    'hasRevisions',
                    'createdAt',
                    'updatedAt',
                    'workflowApprovalStatus'
                ]);
                o.status = !!quotation.paymentPlan && !!quotation.paymentPlan.items ? 'payment-planned' : 'draft';
                o.code = await app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    save: true
                });
                o.partnerGroupId = partner.groupId;
                o.partnerId = partner._id;
                o.reference = quotation.code;
                o.orderDate = app.datetime.local().toJSDate();
                o.relatedDocuments = [
                    {
                        collection: 'sale.quotations',
                        view: 'sale.sales.quotations',
                        title: 'Quotations',
                        ids: [quotation._id]
                    }
                ];

                // Init limit params.
                o.limitParams = await app.rpc('finance.check-partner-limit', {
                    partnerId: o.partnerId,
                    currencyId: o.currencyId,
                    guaranteeId: o.guaranteeId,
                    amount: o.grandTotal,
                    document: 'order'
                });

                if (payload.useCurrentExchangeRate) {
                    await convertCurrencies({app, model: o, company});
                }

                const order = await ordersCollection.create(o, {
                    user: params.user,
                    userLocation: params.userLocation,
                    setLocation: true
                });

                // Update quotation.
                const relatedDocument = {
                    collection: 'sale.orders',
                    view: 'sale.sales.orders',
                    title: 'Orders',
                    ids: [order._id]
                };
                const relatedDocuments = (quotation.relatedDocuments || []).concat([relatedDocument]);
                await quotationsCollection.patch(
                    {_id: quotation._id},
                    {
                        status: 'won',
                        convertedAt: app.datetime.local().toJSDate(),
                        relatedDocuments
                    },
                    {
                        user: params.user,
                        userLocation: params.userLocation,
                        setLocation: true
                    }
                );

                // Create invoice document profits.
                const quotationDocumentProfit = await app.collection('sale.document-profits').findOne({
                    documentId: quotation._id,
                    documentCollection: 'sale.quotations'
                });
                if (_.isPlainObject(quotationDocumentProfit)) {
                    await app.collection('sale.document-profits').create({
                        ..._.omit(quotationDocumentProfit, ['_id', 'documentId', 'documentCollection']),
                        documentId: order._id,
                        documentCollection: 'sale.orders'
                    });
                }

                // Update conversion.
                conversion.orderId = order._id;
                conversion.orderCode = order.code;
                conversion.orderRecordDate = order.recordDate;
                conversion.quotationToOrder = true;
                conversion.date = app.datetime.local().toJSDate();
                conversion.status = 'order';
            } else if (payload.conversionType === 'to-invoice') {
                const numbering = await app.collection('kernel.numbering').findOne(
                    {
                        code: 'accountingCustomerInvoiceNumbering',
                        $select: ['_id']
                    },
                    {
                        disableInUseCheck: true,
                        disableActiveCheck: true,
                        disableSoftDelete: true
                    }
                );

                // Find last due date.
                let dueDate = app.datetime.local().toJSDate();
                if (
                    _.isObject(quotation.paymentPlan) &&
                    _.isObject(quotation.paymentPlan.report) &&
                    _.isDate(quotation.paymentPlan.report.dueDate)
                ) {
                    dueDate = quotation.paymentPlan.report.dueDate;
                }

                // Get scope rate.
                let scopeRate = 1;
                if (!!app.setting('system.scopes')) {
                    if (partner.scope === '1') {
                        scopeRate = 1;
                    } else if (partner.scope === '2') {
                        scopeRate = 0;
                    } else {
                        if (app.setting('system.multiBranch')) {
                            const branch = await app.collection('kernel.branches').findOne({
                                _id: quotation.branchId,
                                $select: ['isScopesActive', 'scope1Rate']
                            });

                            if (_.isPlainObject(branch) && branch.isScopesActive) {
                                scopeRate = branch.scope1Rate / 100;
                            }
                        } else {
                            const company = await app.collection('kernel.company').findOne({
                                $select: ['scope1Rate']
                            });

                            scopeRate = company.scope1Rate / 100;
                        }
                    }
                }

                // Create invoice.
                const i = _.omit(quotation, [
                    'status',
                    'partnerType',
                    'partnerId',
                    'leadId',
                    'stageId',
                    'probability',
                    'quotationDate',
                    'expiryDate',
                    'lostReasonId',
                    'stageHistory',
                    'competitors',
                    'relatedDocuments',
                    'revisionId',
                    'revisionName',
                    'hasRevisions',
                    'createdAt',
                    'updatedAt',
                    'workflowApprovalStatus'
                ]);
                i.status = !!quotation.paymentPlan ? 'payment-planned' : 'draft';
                i.code = await app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    date: app.datetime.local().toJSDate(),
                    save: true
                });
                i.partnerGroupId = partner.groupId;
                i.partnerId = partner._id;
                i.reference = quotation.code;
                i.dueDate = dueDate;
                i.scopeRate = scopeRate;
                i.relatedDocuments = [
                    {
                        collection: 'sale.quotations',
                        view: 'sale.sales.quotations',
                        title: 'Quotations',
                        ids: [quotation._id]
                    }
                ];

                // Extra.
                const journal = await app.collection('accounting.journals').findOne({
                    type: 'sale',
                    branchId: i.branchId,
                    $or: [
                        {currencyId: i.currencyId},
                        {currencyId: {$exists: false}},
                        {currencyId: ''},
                        {currencyId: null}
                    ],
                    $select: ['_id']
                });
                if (_.isObject(journal)) {
                    // Journal id.
                    i.journalId = journal._id;

                    // Journal description.
                    i.journalDescription = `${this.translate('Customer Invoices')} / ${partner.code} - ${partner.name}`;
                } else {
                    // Journal id.
                    i.journalId = '';

                    // Journal description.
                    i.journalDescription = '';
                }
                i.accountingAccountId = partner.accountingAccountId;
                i.invoiceScenario = partner.invoiceScenario;
                i.eInvoiceTypeId = partner.eInvoiceTypeId;

                // Init limit params.
                i.limitParams = await app.rpc('finance.check-partner-limit', {
                    partnerId: i.partnerId,
                    currencyId: i.currencyId,
                    guaranteeId: i.guaranteeId,
                    amount: i.grandTotal,
                    document: 'invoice'
                });

                if (payload.useCurrentExchangeRate) {
                    await convertCurrencies({app, model: i, company});
                }

                const invoice = await invoicesCollection.create(i, {
                    user: params.user,
                    userLocation: params.userLocation,
                    setLocation: true
                });

                // Create invoice document profits.
                const quotationDocumentProfit = await app.collection('sale.document-profits').findOne({
                    documentId: quotation._id,
                    documentCollection: 'sale.quotations'
                });
                if (_.isPlainObject(quotationDocumentProfit)) {
                    await app.collection('sale.document-profits').create({
                        ..._.omit(quotationDocumentProfit, ['_id', 'documentId', 'documentCollection']),
                        documentId: invoice._id,
                        documentCollection: 'accounting.customer-invoices'
                    });
                }

                // Update quotation.
                const relatedDocument = {
                    collection: 'accounting.customer-invoices',
                    view: 'accounting.sales.customer-invoices',
                    title: 'Invoices',
                    ids: [invoice._id]
                };
                const relatedDocuments = (quotation.relatedDocuments || []).concat([relatedDocument]);
                await quotationsCollection.patch(
                    {_id: quotation._id},
                    {
                        status: 'won',
                        convertedAt: app.datetime.local().toJSDate(),
                        relatedDocuments
                    },
                    {
                        user: params.user,
                        userLocation: params.userLocation,
                        setLocation: true
                    }
                );

                // Update conversion.
                conversion.invocieId = invoice._id;
                conversion.invocieCode = invoice.code;
                conversion.invocieRecordDate = invoice.recordDate;
                conversion.quotationToInvoice = true;
                conversion.date = app.datetime.local().toJSDate();
                conversion.status = 'invoice';
            }
        } else {
            const updateData = {};
            const competitorOperations = [];

            updateData.convertedAt = app.datetime.local().toJSDate();

            updateData.lostReasonId = payload.lostReasonId;
            if (payload.lostType === 'lost-to-competitor') {
                updateData.status = 'lost-to-competitor';

                updateData.competitors = quotation.competitors || [];
                const existingIndex = _.findIndex(updateData.competitors, c => c.id === payload.competitorId);
                if (existingIndex === -1) {
                    updateData.competitors.push({
                        id: payload.competitorId,
                        productIds: [],
                        won: true
                    });
                } else {
                    updateData.competitors[existingIndex].won = true;
                }

                for (const competitor of quotation.competitors || []) {
                    const productIds = [];

                    for (const productId of competitor.productIds || []) {
                        if (productIds.indexOf(productId) === -1) {
                            productIds.push(productId);
                        }
                    }

                    if (productIds.length > 0) {
                        competitorOperations.push({
                            updateOne: {
                                filter: {_id: competitor.id},
                                update: {
                                    $addToSet: {
                                        productIds: {$each: productIds}
                                    }
                                }
                            }
                        });
                    }
                }

                if (competitorOperations.length > 0) {
                    await competitorsCollection.bulkWrite(competitorOperations);
                }

                // Update conversion.
                conversion.date = app.datetime.local().toJSDate();
                conversion.status = 'lost-to-competitor';
            } else {
                updateData.status = 'lost';

                // Update conversion.
                conversion.date = app.datetime.local().toJSDate();
                conversion.status = 'lost';
            }

            conversion.quotationToLost = true;

            await quotationsCollection.patch({_id: quotation._id}, updateData, {
                user: params.user
            });

            await app.db.collection('kernel_copyable-items').deleteMany(
                {
                    documentId: quotation._id
                },
                {
                    collation: {locale: app.config('app.locale')}
                }
            );
        }

        // Update conversion.
        await app.collection('sale.conversions').patch({_id: conversion._id}, _.omit(conversion, '_id'));
    }
};

async function convertCurrencies({app, model, company}) {
    const currencies = await app.collection('kernel.currencies').find({
        $select: ['_id', 'name']
    });
    const currencyMap = {};
    const exchangeRatesMap = {};
    const exchangeRates = [];
    const payloads = [];

    for (const currency of currencies) {
        if (currency.name === company.currency.name) continue;

        payloads.push({
            from: currency.name,
            to: company.currency.name,
            value: 1,
            options: {date: app.datetime.local().toJSDate()}
        });
    }

    const conversionResults = await app.rpc('kernel.common.convert-currencies', payloads);

    for (const payload of conversionResults) {
        const currency = currencies.find(c => c.name === payload.from);
        if (currency) currencyMap[currency._id] = payload;

        exchangeRates.push({
            currencyName: payload.from,
            rate: payload.rate
        });
        exchangeRatesMap[payload.from] = payload.rate;
    }
    model.exchangeRates = exchangeRates;
    model.exchangeRatesMap = exchangeRatesMap;

    if (
        company.currencyId !== model.currencyId &&
        !!currencyMap[model.currencyId] &&
        _.isFinite(currencyMap[model.currencyId].rate)
    ) {
        model.currencyRate = currencyMap[model.currencyId].rate;
    }

    for (const item of model.items) {
        if (
            company.currencyId !== item.currencyId &&
            !!currencyMap[item.currencyId] &&
            _.isFinite(currencyMap[item.currencyId].rate)
        ) {
            item.currencyRate = currencyMap[item.currencyId].rate;
        }
    }

    model.items = await app.rpc('sale.decorate-quotation-items', {
        items: model.items,
        field: 'unitPrice',
        model: _.omit(model, 'items'),
        productFields: ['code', 'name', 'displayName', 'definition', 'type', 'baseUnitId', 'barcode']
    });
}
