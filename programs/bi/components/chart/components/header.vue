<template>
    <div ref="header"
         class="bi-chart-header"
         :class="{'is-title-centered': chartType.centerTitle && !isEditable}">
        <div class="chart-header-title-container">
            <template v-if="chart.id">
                <input class="chart-header-editable-title"
                       ref="titleInput"
                       v-model="title"
                       @blur="handleTitleInputBlur"
                       v-if="isEditing"/>
                <div class="chart-header-title"
                     :class="{'is-editable': isEditable}"
                     @click="handleTitleClick"
                     v-else>
                    <i class="fas fa-plus-circle" v-if="isEditable && !hasTitle"></i>
                    {{ title }}
                </div>
            </template>
            <template v-else>
                <div class="chart-header-title">
                    {{ chartTypeTitle }}
                </div>
            </template>
        </div>

        <div class="chart-header-actions"
             :class="{'is-visible': isMoreActionsVisible}"
             v-if="chart.id && !forPreview">
            <div class="chart-header-action"
                 @click="$emit('edit')"
                 v-if="isEditable">
                <i class="fal fa-pencil-alt"></i>
            </div>
            <div class="chart-header-action" v-else>
                <i class="fal fa-expand-alt"></i>
            </div>

            <el-dropdown @command="handleMoreAction"
                         trigger="click"
                         placement="bottom-end"
                         @visible-change="isVisible => isMoreActionsVisible = isVisible">
                <div class="chart-header-action" :class="{'is-active': isMoreActionsVisible}">
                    <i class="fal fa-ellipsis-h" style="font-size: 27px"></i>
                </div>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit" icon="fal fa-pencil-alt" v-if="isEditable">
                        {{$t('Edit')}}
                    </el-dropdown-item>
                    <el-dropdown-item command="remove" icon="fal fa-trash-alt" class="text-danger" v-if="isEditable">
                        {{ $t('Remove') }}
                    </el-dropdown-item>

                    <el-dropdown-item command="download-as-pdf" icon="fal fa-file-pdf" v-if="!isEditable">
                        {{ $t('Download as pdf') }}
                    </el-dropdown-item>
                    <el-dropdown-item command="download-as-image" icon="fal fa-file-image" v-if="!isEditable">
                        {{ $t('Download as image') }}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
    </div>
</template>

<script>
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import saveAs from 'framework/save-as';

export default {
    props: {
        chart: Object,
        chartType: Object,
        isValid: {
            type: Boolean,
            default: false
        },
        forPreview: {
            type: Boolean,
            default: false
        },
        isEditable: {
            type: Boolean,
            default: false
        }
    },

    data: () => ({
        title: '',
        isMoreActionsVisible: false,
        hasTitle: false,
        isEditing: false
    }),

    computed: {
        chartTypeTitle() {
            return this.$t(this.chartType.title);
        }
    },

    watch: {
        'chart.title'(value, oldValue) {
            if (this.isEditing) return;

            let title = this.title
            .trim()
            .replace(this.$t('Click to add title'), '')
            .replace(this.$t('No title'), '');

            if (value !== oldValue && value !== title) {
                value = (value || '').trim();

                if (!value) {
                    if (this.isEditable) {
                        this.title = this.$t('Click to add title');
                    } else {
                        this.title = this.$t('No title');
                    }
                } else {
                    this.title = value;
                    this.hasTitle = true;
                }
            }
        },
        isEditable(value) {
            if (this.isEditing) return;

            this.title = this.title
            .trim()
            .replace(this.$t('Click to add title'), '')
            .replace(this.$t('No title'), '');

            if (!this.title) {
                if (value) {
                    this.title = this.$t('Click to add title');
                } else {
                    this.title = this.$t('No title');
                }
            } else {
                this.hasTitle = true;
            }
        }
    },

    methods: {
        handleTitleClick() {
            if (!this.isEditable) {
                return;
            }

            if (!this.hasTitle) {
                this.title = '';
            }

            this.isEditing = true;

            this.$nextTick(() => {
                this.$refs.titleInput.focus();
            });
        },
        async handleTitleInputBlur() {
            this.title = this.title
            .trim()
            .replace(this.$t('Click to add title'), '')
            .replace(this.$t('No title'), '');

            if (!this.title) {
                this.title = this.$t('Click to add title');
                this.hasTitle = false;

                this.$emit('title-changed', {
                    ...this.chart,
                    title: ''
                });
            } else {
                this.hasTitle = true;

                this.$emit('title-changed', {
                    ...this.chart,
                    title: this.title
                });
            }

            this.$nextTick(() => {
                this.isEditing = false;
            });
        },
        async handleMoreAction(action) {
            if (action === 'edit') {
                this.$emit('edit');
            } else if (action === 'remove') {
                this.$program.alert('confirm', this.$t('Are you sure you want to delete this record?'), async confirmed => {
                    if (!confirmed) return;

                    this.$emit('removed');

                    await this.$collection('bi.charts').remove({id: this.chart.id});
                });
            } else if (action === 'download-as-image') {
                await this.handleDownloadAsImage();
            } else if (action === 'download-as-pdf') {
                await this.handleDownloadAsPdf();
            }
        },

        async handleDownloadAsImage() {
            this.$params('loading', true);

            try {
                const chartEl = this.$refs.header.closest('.bi-chart');
                if (!chartEl) {
                    throw new Error('Chart element not found');
                }

                chartEl.classList.add('is-exporting');

                // Wait a bit for any dynamic content to render
                await new Promise(resolve => setTimeout(resolve, 100));

                const canvas = await html2canvas(chartEl, {
                    backgroundColor: '#ffffff',
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: chartEl.scrollWidth || chartEl.offsetWidth,
                    height: chartEl.scrollHeight || chartEl.offsetHeight
                });

                chartEl.classList.remove('is-exporting');

                canvas.toBlob(blob => {
                    const fileName = this.chart.title || 'chart';
                    saveAs(blob, `${fileName}.png`);

                    this.$nextTick(() => {
                        this.$params('loading', false);
                    });
                });
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        },

        async handleDownloadAsPdf() {
            this.$params('loading', true);

            try {
                const chartEl = this.$refs.header.closest('.bi-chart');
                if (!chartEl) {
                    throw new Error('Chart element not found');
                }

                chartEl.classList.add('is-exporting');

                // Wait a bit for any dynamic content to render
                await new Promise(resolve => setTimeout(resolve, 100));

                const canvas = await html2canvas(chartEl, {
                    backgroundColor: '#ffffff',
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: chartEl.scrollWidth || chartEl.offsetWidth,
                    height: chartEl.scrollHeight || chartEl.offsetHeight
                });

                chartEl.classList.remove('is-exporting');

                const imgData = canvas.toDataURL('image/png');

                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                const ratio = imgHeight / imgWidth;

                const a4Width = 210;
                const a4Height = 297;

                let pdfWidth, pdfHeight;

                if (ratio > a4Height / a4Width) {
                    pdfHeight = a4Height;
                    pdfWidth = a4Height / ratio;
                } else {
                    pdfWidth = a4Width;
                    pdfHeight = a4Width * ratio;
                }

                const pdf = new jsPDF({
                    orientation: pdfWidth > pdfHeight ? 'landscape' : 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                const x = (a4Width - pdfWidth) / 2;
                const y = (a4Height - pdfHeight) / 2;

                pdf.addImage(imgData, 'PNG', x, y, pdfWidth, pdfHeight);

                const fileName = this.chart.title || 'chart';
                pdf.save(`${fileName}.pdf`);

                this.$params('loading', false);
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        }
    },

    created() {
        if (!!this.chart) {
            this.title = (this.chart.title || '').trim();
        }
        if (!this.title) {
            if (this.isEditable) {
                this.title = this.$t('Click to add title');
            } else {
                this.title = this.$t('No title');
            }
        } else {
            this.hasTitle = true;
        }
    }
};
</script>
