<template>
    <div class="studio-toolbar-actions">
        <el-dropdown
            @command="handleAction"
            trigger="click"
            placement="bottom-end"
            @visible-change="isVisible => (isActionsVisible = isVisible)"
        >
            <el-button :class="{'is-actions-visible': isActionsVisible}" size="small" icon="fal fa-ellipsis-h" plain />
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="download-as-pdf" icon="fal fa-file-pdf">
                    {{ $t('Download as pdf') }}
                </el-dropdown-item>
                <el-dropdown-item command="download-as-image" icon="fal fa-file-image">
                    {{ $t('Download as image') }}
                </el-dropdown-item>
                <el-dropdown-item command="dashboard-settings" icon="fal fa-cog" v-if="canEdit">
                    {{ $t('Dashboard settings') }}
                </el-dropdown-item>
                <el-dropdown-item
                    command="delete-dashboard"
                    icon="fal fa-trash-alt"
                    class="text-danger"
                    v-if="canRemove"
                >
                    {{ $t('Delete dashboard') }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>

<script>
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import saveAs from 'framework/save-as';
import store from '../../store';

export default {
    data: () => ({
        isActionsVisible: false
    }),

    computed: {
        canEdit() {
            return this.$app.hasPermission({
                type: 'record',
                name: 'bi.dashboards',
                method: 'update'
            });
        },
        canRemove() {
            return this.$app.hasPermission({
                type: 'record',
                name: 'bi.dashboards',
                method: 'remove'
            });
        },
        dashboard() {
            return store.state.dashboard;
        }
    },

    methods: {
        async handleAction(action) {
            if (action === 'download-as-image') {
                await this.handleDownloadAsImage();
            } else if (action === 'download-as-pdf') {
                await this.handleDownloadAsPdf();
            } else if (action === 'dashboard-settings') {
                this.handleDashboardSettings();
            } else if (action === 'delete-dashboard') {
                this.handleDeleteDashboard();
            }
        },

        async handleDownloadAsImage() {
            this.$params('loading', true);

            try {
                const dashboardEl = document.querySelector('.bi-dashboard-studio');
                if (!dashboardEl) {
                    throw new Error('Dashboard element not found');
                }


                const layoutEl = dashboardEl.querySelector('.layout-container');
                const targetEl = layoutEl || dashboardEl;

                dashboardEl.classList.add('is-exporting');
                targetEl.classList.add('is-exporting');

                await new Promise(resolve => setTimeout(resolve, 100));

                const canvas = await html2canvas(targetEl, {
                    backgroundColor: '#ffffff',
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: targetEl.scrollWidth || targetEl.offsetWidth,
                    height: targetEl.scrollHeight || targetEl.offsetHeight
                });

                dashboardEl.classList.remove('is-exporting');
                targetEl.classList.remove('is-exporting');

                canvas.toBlob(blob => {
                    const fileName = this.dashboard?.title || 'dashboard';
                    saveAs(blob, `${fileName}.png`);

                    this.$nextTick(() => {
                        this.$params('loading', false);
                    });
                });
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        },

        async handleDownloadAsPdf() {
            this.$params('loading', true);

            try {
                const dashboardEl = document.querySelector('.bi-dashboard-studio');
                if (!dashboardEl) {
                    throw new Error('Dashboard element not found');
                }

                const layoutEl = dashboardEl.querySelector('.layout-container');
                const targetEl = layoutEl || dashboardEl;

                dashboardEl.classList.add('is-exporting');
                targetEl.classList.add('is-exporting');

                await new Promise(resolve => setTimeout(resolve, 100));

                const canvas = await html2canvas(targetEl, {
                    backgroundColor: '#ffffff',
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: targetEl.scrollWidth || targetEl.offsetWidth,
                    height: targetEl.scrollHeight || targetEl.offsetHeight
                });

                dashboardEl.classList.remove('is-exporting');
                targetEl.classList.remove('is-exporting');

                const imgData = canvas.toDataURL('image/png');

                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                const ratio = imgHeight / imgWidth;

                const a4Width = 210;
                const a4Height = 297;

                let pdfWidth, pdfHeight;

                if (ratio > a4Height / a4Width) {
                    pdfHeight = a4Height;
                    pdfWidth = a4Height / ratio;
                } else {
                    pdfWidth = a4Width;
                    pdfHeight = a4Width * ratio;
                }

                const pdf = new jsPDF({
                    orientation: pdfWidth > pdfHeight ? 'landscape' : 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                const x = (a4Width - pdfWidth) / 2;
                const y = (a4Height - pdfHeight) / 2;

                pdf.addImage(imgData, 'PNG', x, y, pdfWidth, pdfHeight);

                const fileName = this.dashboard?.title || 'dashboard';
                pdf.save(`${fileName}.pdf`);

                this.$params('loading', false);
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        }
    }
};
</script>
