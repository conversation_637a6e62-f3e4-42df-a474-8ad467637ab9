import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'partners',
    title: 'Partners',
    branch: 'multiple',
    view: 'partners.partners',
    labelParams: {
        template: '{{code}} - {{name}}',
        extraFields: ['code', 'name']
    },
    locationFrom: 'address',
    assignable: true,
    extraIndexes: [{type: 'normal', key: 'additionalInformation.*', value: 1}],
    schema: {
        // General
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['customer', 'vendor', 'employee'],
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            index: true,
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        isCompany: {
            type: 'boolean',
            label: 'Is corporate',
            default: false
        },
        isExternalCustomer: {
            type: 'boolean',
            label: 'Is external customer',
            default: false,
            index: true
        },
        isExternalVendor: {
            type: 'boolean',
            label: 'Is external vendor',
            default: false,
            index: true
        },
        isExternalEmployee: {
            type: 'boolean',
            label: 'Is external employee',
            default: false,
            index: true
        },
        userAccountId: {
            type: 'string',
            label: 'User',
            required: false,
            index: true
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD',
            index: true,
            required: false
        },
        languageId: {
            type: 'string',
            label: 'Language'
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        groupId: {
            type: 'string',
            label: 'Partner group',
            index: true
        },
        website: {
            type: 'string',
            label: 'Website',
            required: false
        },
        workAddressId: {
            type: 'string',
            label: 'Work address',
            required: false
        },
        workLocation: {
            type: 'string',
            label: 'Work location',
            required: false
        },
        workingHoursId: {
            type: 'string',
            label: 'Working hours',
            required: false,
            index: true
        },
        homeWorkDistance: {
            type: 'decimal',
            label: 'Home-work distance',
            required: false
        },
        timezone: {
            type: 'string',
            label: 'Timezone',
            index: true
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        },
        countryId: {
            type: 'string',
            label: 'Country',
            index: true
        },
        identity: {
            type: 'string',
            label: 'Identity no',
            required: false,
            index: true
        },
        passportNo: {
            type: 'string',
            label: 'Passport no',
            required: false,
            unique: true
        },
        legalName: {
            type: 'string',
            label: 'Legal name',
            required: false
        },
        tin: {
            type: 'string',
            label: 'TIN',
            required: false,
            index: true
        },
        taxDepartment: {
            type: 'string',
            label: 'Tax department',
            required: false
        },
        isGovernmentOffice: {
            type: 'boolean',
            label: 'Government office',
            default: false
        },
        isVerified: {
            type: 'boolean',
            label: 'Is verified',
            required: false,
            index: true
        },
        tags: {
            type: [
                {
                    label: {
                        type: 'string',
                        index: true
                    },
                    color: {
                        type: 'string',
                        index: true
                    }
                }
            ],
            label: 'Tags',
            default: [],
            index: true
        },
        departmentId: {
            type: 'string',
            label: 'Department',
            required: false,
            index: true
        },
        departmentPath: {
            type: 'string',
            label: 'Department',
            required: false,
            index: true
        },
        positionId: {
            type: 'string',
            label: 'Job position',
            required: false,
            index: true
        },
        workingTypeId: {
            type: 'string',
            label: 'Working type',
            required: false
        },
        managerId: {
            type: 'string',
            label: 'Manager',
            required: false,
            index: true
        },
        instructorId: {
            type: 'string',
            label: 'Instructor',
            required: false,
            index: true
        },
        phone: {
            type: 'string',
            label: 'Phone',
            required: false,
            index: true
        },
        phoneCountryCode: {
            type: 'string',
            label: 'Phone country code',
            required: false
        },
        phoneNumbers: {
            type: [
                {
                    type: 'object',
                    blackbox: true
                }
            ],
            label: 'Phone Numbers',
            default: []
        },
        address: {
            type: 'object',
            blackbox: true,
            required: false
        },
        gln: {
            type: 'string',
            label: 'GLN',
            required: false,
            index: true
        },
        avatar: {
            type: 'string',
            label: 'Avatar',
            required: false
        },
        system: {
            type: 'boolean',
            default: false
        },

        // Details
        mainPartner: {
            type: 'boolean',
            label: 'Is main partner',
            default: false
        },
        connectedPartnerIds: {
            type: ['string'],
            label: 'Connected partners',
            default: []
        },
        firstName: {
            type: 'string',
            label: 'First name',
            required: false,
            index: true
        },
        lastName: {
            type: 'string',
            label: 'Last name',
            required: false,
            index: true
        },
        occupationId: {
            type: 'string',
            label: 'Occupation',
            required: false
        },
        tagline: {
            type: 'string',
            label: 'Tagline',
            required: false
        },
        sectorId: {
            type: 'string',
            label: 'Sector of activity',
            required: false
        },
        annualIncome: {
            type: 'string',
            label: 'Annual income',
            required: false
        },
        numberOfEmployees: {
            type: 'integer',
            label: 'Number of employees',
            min: 0,
            required: false
        },
        insuranceNumber: {
            type: 'string',
            label: 'Insurance number',
            required: false
        },
        insuranceStatus: {
            type: 'string',
            label: 'Insurance status',
            default: 'normal',
            required: false
        },
        exConvict: {
            type: 'boolean',
            label: 'Ex-convict',
            default: false
        },
        visaNo: {
            type: 'string',
            label: 'Visa no',
            required: false
        },
        workPermitNo: {
            type: 'string',
            label: 'Work permit no',
            required: false
        },
        visaExpireDate: {
            type: 'date',
            label: 'Visa expire date',
            required: false
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        birthdate: {
            type: 'date',
            label: 'Birthdate',
            required: false
        },
        birthPlace: {
            type: 'string',
            label: 'Place of birth',
            required: false
        },
        birthCountryId: {
            type: 'string',
            label: 'Country of birth',
            required: false
        },
        gender: {
            type: 'string',
            label: 'Gender',
            default: 'male',
            allowed: ['male', 'female']
        },
        maritalStatus: {
            type: 'string',
            label: 'Marital status',
            default: 'single'
        },
        numberOfChildren: {
            type: 'integer',
            label: 'Number of children',
            min: 0,
            default: 0
        },
        hasDisabledChildren: {
            type: 'boolean',
            label: 'Has disabled children',
            default: false
        },
        numberOfDisabledChildren: {
            type: 'integer',
            label: 'Number of disabled children',
            min: 0,
            default: 0
        },
        hasOtherDependentPeople: {
            type: 'boolean',
            label: 'Has other dependent people',
            default: false
        },
        numberOfDependentSeniors: {
            type: 'integer',
            label: 'Seniors (>=65)',
            min: 0,
            default: 0
        },
        numberOfDependentDisabledSeniors: {
            type: 'integer',
            label: 'Disabled seniors (>=65)',
            min: 0,
            default: 0
        },
        numberOfDependentPeople: {
            type: 'integer',
            label: 'People (<65)',
            min: 0,
            default: 0
        },
        numberOfDependentDisabledPeople: {
            type: 'integer',
            label: 'Disabled people (<65)',
            min: 0,
            default: 0
        },
        educationInformation: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        foreignLanguages: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        certificates: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        exams: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        skills: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        drivingLicences: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        workExperiences: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        references: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],

        // Health
        bloodGroup: {
            type: 'string',
            label: 'Blood group',
            required: false
        },
        age: {
            type: 'integer',
            label: 'Age',
            required: false
        },
        disabledCondition: {
            type: 'boolean',
            label: 'Disabled condition',
            default: false
        },
        disabledCategoryId: {
            type: 'string',
            label: 'Disabled category',
            required: false
        },
        disabledDegreeId: {
            type: 'string',
            label: 'Disabled degree',
            required: false
        },
        disabledRate: {
            type: 'string',
            label: 'Disabled rate',
            required: false
        },
        emergencyContactId: {
            type: 'string',
            label: 'Contact',
            required: false
        },
        emergencyDegreeOfKinshipId: {
            type: 'string',
            label: 'Degree of kinship',
            required: false
        },
        emergencyContactPhone: {
            type: 'string',
            label: 'Phone',
            required: false
        },
        medicalNote: {
            type: 'string',
            label: 'Medical note',
            required: false
        },
        examinations: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        diseases: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        workAccidents: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],

        // Contacts
        contactIds: {
            type: ['string'],
            default: [],
            index: true
        },

        // Sale
        priceListId: {
            type: 'string',
            label: 'Price list',
            required: false,
            index: true
        },
        salesOrganizationId: {
            type: 'string',
            label: 'Organization',
            required: false
        },
        salesManagerId: {
            type: 'string',
            label: 'Sales manager',
            required: false
        },
        salespersonId: {
            type: 'string',
            label: 'Salesperson',
            required: false
        },

        // Purchase.
        listPriceId: {
            type: 'string',
            label: 'List price',
            required: false,
            index: true
        },
        purchaseOrganizationId: {
            type: 'string',
            label: 'Organization',
            required: false
        },
        purchaseManagerId: {
            type: 'string',
            label: 'Purchase manager',
            required: false
        },
        purchaseRepresentativeId: {
            type: 'string',
            label: 'Purchase representative',
            required: false
        },

        // Finance
        paymentTermId: {
            type: 'string',
            label: 'Payment term',
            required: false
        },
        acceptEndorsedCheques: {
            type: 'boolean',
            label: 'This partner accepts endorsed cheques',
            default: false
        },
        chequesEndorsable: {
            type: 'boolean',
            label: 'This partner cheques can be endorsed',
            default: false
        },
        acceptEndorsedPromissoryNotes: {
            type: 'boolean',
            label: 'This partner accepts endorsed promissory notes',
            default: false
        },
        promissoryNotesEndorsable: {
            type: 'boolean',
            label: 'This partner promissory notes can be endorsed',
            default: false
        },
        enableLimitChecks: {
            type: 'boolean',
            label: 'Enable limit checks',
            default: false,
            index: true
        },
        limitControlDocument: {
            type: 'string',
            label: 'Limit control document',
            default: 'order'
        },
        limitOrderControl: {
            type: 'string',
            label: 'Limit order control',
            default: 'warn'
        },
        limitQuotationControl: {
            type: 'string',
            label: 'Limit quotation control',
            default: 'warn'
        },
        limitInvoiceControl: {
            type: 'string',
            label: 'Limit invoice control',
            default: 'block'
        },
        blacklist: {
            type: 'boolean',
            label: 'Blacklist',
            default: false,
            index: true
        },
        openAccountLimit: {
            type: 'decimal',
            label: 'Open account limit',
            default: 0
        },
        guaranteeLimit: {
            type: 'decimal',
            label: 'Guarantees limit',
            default: 0
        },
        accountLimit: {
            type: 'decimal',
            label: 'Account limit',
            default: 0
        },
        riskLimit: {
            type: 'decimal',
            label: 'Risk limit',
            default: 0
        },
        totalLimit: {
            type: 'decimal',
            label: 'Total limit',
            default: 0
        },
        guarantees: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        creditCardIds: {
            type: [
                {
                    type: 'string',
                    required: false
                }
            ],
            default: []
        },
        bankAccountIds: {
            type: [
                {
                    type: 'string',
                    required: false
                }
            ],
            default: []
        },

        // Accounting,
        accountingAccountId: {
            type: 'string',
            label: 'Account',
            required: false
        },
        additionalAllowanceAccount: {
            type: 'string',
            label: 'Additional allowance account',
            required: false
        },
        employeeExpenseAccountId: {
            type: 'string',
            label: 'Expense account',
            required: false
        },
        employeeAccountGroupId: {
            type: 'string',
            label: 'Account group',
            required: false
        },
        scope: {
            type: 'string',
            label: 'Scope',
            default: '1+2'
        },
        salesTaxId: {
            type: 'string',
            label: 'Sales tax',
            required: false
        },
        purchaseTaxId: {
            type: 'string',
            label: 'Purchase tax',
            required: false
        },
        invoiceScenario: {
            type: 'string',
            label: 'Invoice scenario',
            default: 'normal'
        },
        eInvoiceTypeId: {
            type: 'string',
            label: 'E-Invoice type',
            required: false
        },
        waybillType: {
            type: 'string',
            label: 'Waybill type',
            default: 'normal'
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal
        additionalInformation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        password: {
            type: 'string',
            label: 'Password',
            required: false
        },
        hashedPassword: {
            type: 'string',
            label: 'Hashed password',
            required: false
        },
        passwordResetToken: {
            type: 'string',
            required: false
        },
        passwordResetExpires: {
            type: 'datetime',
            required: false
        },
        isSubscribedToNewsletter: {
            type: 'boolean',
            required: false
        },
        extra: {
            currentContractId: {
                type: 'string',
                required: false
            },
            currentContractRuleCode: {
                type: 'string',
                required: false
            }
        },
        // Used by ecommerce (Trendyol, Amazon etc.)
        integrationId: {
            type: 'string',
            label: 'Integration ID',
            required: false,
            index: true
        },
        defaultDeliveryOptionId: {
            type: 'string',
            label: 'Default delivery option',
            required: false
        },
        // For production
        kioskAccessCode: {
            type: 'string',
            label: 'Kiosk access code',
            required: false
        }
    },
    attributes: {
        language: {
            collection: 'kernel.languages',
            parentField: 'languageId',
            childField: '_id'
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        country: {
            collection: 'kernel.countries',
            parentField: 'countryId',
            childField: '_id'
        },
        occupation: {
            collection: 'kernel.occupations',
            parentField: 'occupationId',
            childField: '_id'
        },
        group: {
            collection: 'kernel.partner-groups',
            parentField: 'groupId',
            childField: '_id'
        },
        department: {
            collection: 'hr.departments',
            parentField: 'departmentId',
            childField: '_id'
        },
        position: {
            collection: 'kernel.positions',
            parentField: 'positionId',
            childField: '_id'
        },
        workingType: {
            collection: 'hr.working-types',
            parentField: 'workingTypeId',
            childField: '_id'
        },
        manager: {
            collection: 'kernel.partners',
            parentField: 'managerId',
            childField: '_id'
        },
        instructor: {
            collection: 'kernel.partners',
            parentField: 'instructorId',
            childField: '_id'
        },
        sector: {
            collection: 'kernel.sectors-of-activity',
            parentField: 'sectorId',
            childField: '_id'
        },
        accountingAccount: {
            collection: 'kernel.accounts',
            parentField: 'accountingAccountId',
            childField: '_id'
        },
        branches: {
            collection: 'kernel.branches',
            parentField: 'branchIds',
            childField: '_id'
        },
        paymentTerm: {
            collection: 'finance.payment-terms',
            parentField: 'paymentTermId',
            childField: '_id'
        }
    },
    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['code', 'name', 'email', 'identity', 'tin', 'phone']));

        if (_.isPlainObject(document.additionalInformation)) {
            for (const value of Object.values(document.additionalInformation)) {
                values.push(value);
            }
        }

        return values;
    },
    hooks: {
        before: {
            create: [checkIfIdentityOrTINIsUnique],
            update: [checkIfIdentityOrTINIsUnique],
            patch: [checkIfIdentityOrTINIsUnique],
            remove: []
        },
        after: {
            create: [
                setPrimaryPhone,
                fixContactsPartnerId,
                //setPaymentTermId,
                setAccountingAccountId,
                syncUser,
                clearTags,
                cacheDistinctAccountingAccounts
            ],
            update: [
                setPrimaryPhone,
                //setPaymentTermId,
                setAccountingAccountId,
                syncUser,
                clearTags,
                cacheDistinctAccountingAccounts
            ],
            patch: [
                setPrimaryPhone,
                //setPaymentTermId,
                setAccountingAccountId,
                syncUser,
                clearTags,
                cacheDistinctAccountingAccounts
            ],
            remove: [clearTags]
        }
    }
};

async function checkIfIdentityOrTINIsUnique(context) {
    const data = Array.isArray(context.data) ? context.data : [context.data];

    for (const item of data) {
        if (!_.isPlainObject(item) || (!item.identity && !item.tin)) continue;

        if (
            item.identity === '1111111' ||
            item.identity === '2222222' ||
            item.identity === '********' ||
            item.identity === '********' ||
            item.identity === '********1' ||
            item.identity === '********2' ||
            item.identity === '********11' ||
            item.identity === '********22' ||
            item.identity === '********111' ||
            item.identity === '********222' ||
            item.identity === '********1111' ||
            item.identity === '********2222' ||
            item.tin === '1111111' ||
            item.tin === '2222222' ||
            item.tin === '********' ||
            item.tin === '********' ||
            item.tin === '********1' ||
            item.tin === '********2' ||
            item.tin === '********11' ||
            item.tin === '********22' ||
            item.tin === '********111' ||
            item.tin === '********222' ||
            item.tin === '********1111' ||
            item.tin === '********2222'
        ) {
            continue;
        }

        let extraQuery = {};
        if (Array.isArray(item.connectedPartnerIds) && item.connectedPartnerIds.length > 0) {
            extraQuery._id = {$nin: item.connectedPartnerIds};
        }

        if (!!item.isCompany && !!item.tin) {
            const existingCount = await context.app.collection('kernel.partners').count({
                type: item.type,
                tin: item.tin,
                ...extraQuery
            });

            if (existingCount > 0) {
                throw new context.app.errors.Unprocessable({
                    field: 'tin',
                    message: context.translate('{{label}} must be unique!', {
                        label: context.translate('TIN')
                    })
                });
            }
        } else if (!!item.identity) {
            const existingCount = await context.app.collection('kernel.partners').count({
                type: item.type,
                identity: item.identity,
                ...extraQuery
            });

            if (existingCount > 0) {
                throw new context.app.errors.Unprocessable({
                    field: 'identity',
                    message: context.translate('{{label}} must be unique!', {
                        label: context.translate('Identity no')
                    })
                });
            }
        }
    }

    return context;
}

async function setPrimaryPhone(context) {
    const app = context.app;
    const company = await app.collection('kernel.company').findOne({$select: ['address.countryId']});
    const defaultCountry = await app.collection('kernel.countries').findOne({
        _id: company.address.countryId,
        $select: ['code', 'phoneCode']
    });

    for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
        if (Array.isArray(result.phoneNumbers) && result.phoneNumbers.length > 0) {
            const phoneCountryCode = result.phoneNumbers[0].countryCode || defaultCountry.code;
            const phoneCode = result.phoneNumbers[0].phoneCode || defaultCountry.phoneCode;
            const phone = `${phoneCode} ${result.phoneNumbers[0].number}`;

            await context.app.db.collection('kernel_partners').updateOne(
                {_id: new ObjectId(result._id)},
                {
                    $set: {
                        phone,
                        phoneCountryCode
                    }
                },
                {
                    collation: {locale: context.app.config('app.locale')}
                }
            );

            result.phone = phone;
            result.phoneCountryCode = phoneCountryCode;
        }
    }

    try {
        app.memoryCache.clear(`aggregation-caches-kernel.partners`);
        app.memoryCache.clear(`records-caches-kernel.partners`);
    } catch (e) {
        console.log(e);
    }

    return context;
}

function syncUser(context) {
    if (context.params.disableSyncUser) {
        delete context.params.disableSyncUser;

        return context;
    }

    const result = context.result;

    if (!!result.userAccountId) {
        context.app.collection('kernel.users').patch(
            {_id: result.userAccountId},
            result.deleted
                ? {partnerId: null}
                : {
                      name: result.name,
                      email: result.email,
                      languageId: result.languageId,
                      partnerId: result._id,
                      avatar: result.avatar
                  },
            {
                disableSyncPartner: true
            }
        );
    }

    return context;
}

async function fixContactsPartnerId(context) {
    const result = Array.isArray(context.result) ? context.result : [context.result];

    const operations = [];

    for (const item of result) {
        for (const contactId of item.contactIds || []) {
            operations.push({
                updateOne: {
                    filter: {_id: contactId},
                    update: {$set: {partnerId: item._id}}
                }
            });
        }
    }

    if (operations.length > 0) {
        await context.app.collection('kernel.contacts').bulkWrite(operations);
    }

    return context;
}

async function setPaymentTermId(context) {
    const result = Array.isArray(context.result) ? context.result : [context.result];

    if (result.length < 1) return context;

    const operations = [];

    for (const item of result) {
        if (!item.paymentTermId) {
            const paymentTerm = await context.app.collection('finance.payment-terms').findOne({
                system: true,
                $select: ['_id']
            });

            if (_.isObject(paymentTerm)) {
                item.paymentTermId = paymentTerm._id;

                operations.push({
                    updateOne: {
                        filter: {_id: new ObjectId(item._id)},
                        update: {$set: {paymentTermId: paymentTerm._id}}
                    }
                });
            }
        }
    }

    if (operations.length > 0) {
        await context.app.collection('kernel.partners').bulkWrite(operations);
    }

    return context;
}

async function setAccountingAccountId(context) {
    const result = Array.isArray(context.result) ? context.result : [context.result];

    if (result.length < 1) return context;

    const company = await context.app.collection('kernel.company').findOne({$select: ['address.countryId']});
    const operations = [];

    for (const item of result) {
        if (!item.accountingAccountId || !_.isString(item.accountingAccountId) || _.isEmpty(item.accountingAccountId)) {
            let accountId = null;

            if (item.countryId !== company.address.countryId) {
                if (item.type === 'customer') {
                    accountId = context.app.defaultAccountingAccount('foreignAccountsReceivableAccount', 'sale');
                } else if (item.type === 'vendor') {
                    accountId = context.app.defaultAccountingAccount('foreignAccountsPayableAccount', 'purchase');
                } else if (item.type === 'employee') {
                    accountId = context.app.defaultAccountingAccount('employeeAccount');
                }
            } else {
                if (item.type === 'customer') {
                    accountId = context.app.defaultAccountingAccount('domesticAccountsReceivableAccount', 'sale');
                } else if (item.type === 'vendor') {
                    accountId = context.app.defaultAccountingAccount('domesticAccountsPayableAccount', 'purchase');
                } else if (item.type === 'employee') {
                    accountId = context.app.defaultAccountingAccount('employeeAccount');
                }
            }

            item.accountingAccountId = accountId;

            operations.push({
                updateOne: {
                    filter: {_id: new ObjectId(item._id)},
                    update: {$set: {accountingAccountId: accountId}}
                }
            });
        }
    }

    if (operations.length > 0) {
        await context.app.collection('kernel.partners').bulkWrite(operations);
    }

    return context;
}

function clearTags(context) {
    const app = context.app;

    const result = Array.isArray(context.result) ? context.result : [context.result];

    if (result.length < 1) return context;
    if (!result.some(partner => (partner.tags || []).length > 0)) return context;

    app.cache.delete('partner-tags');

    return context;
}

async function cacheDistinctAccountingAccounts(context) {
    const app = context.app;
    const accountIds = await app.cache.get('distinct-partner-accounting-accounts');

    if (Array.isArray(accountIds)) {
        const result = Array.isArray(context.result) ? context.result : [context.result];
        let changed = false;

        for (const item of result) {
            if (!!item.accountingAccountId && !accountIds.includes(item.accountingAccountId)) {
                accountIds.push(item.accountingAccountId);

                changed = true;
            }
        }

        if (changed) {
            await app.cache.set('distinct-partner-accounting-accounts', accountIds);
        }
    }

    return context;
}
